{"name": "augment-balance", "displayName": "Augment Balance", "description": "Display remaining quota for Augment Code API usage", "version": "1.0.2", "publisher": "g<PERSON><PERSON><PERSON>", "license": "MIT", "author": {"name": "g<PERSON><PERSON><PERSON>"}, "homepage": "https://github.com/gacjie/augment-balance", "repository": {"type": "git", "url": "https://github.com/gacjie/augment-balance.git"}, "bugs": {"url": "https://github.com/gacjie/augment-balance/issues"}, "engines": {"vscode": "^1.74.0"}, "categories": ["Other"], "keywords": ["augment", "balance", "quota", "api"], "icon": "favicon.ico", "galleryBanner": {"color": "#C80000", "theme": "dark"}, "activationEvents": ["onStartupFinished"], "main": "./dist/extension.js", "contributes": {"configuration": {"title": "Augment Balance", "properties": {"augmentBalance.token": {"type": "string", "default": "", "description": "API token for Augment Code", "scope": "application"}, "augmentBalance.updateInterval": {"type": "number", "default": 600, "minimum": 60, "maximum": 3600, "description": "Data update interval in seconds", "scope": "application"}}}, "commands": [{"command": "augmentBalance.openSettings", "title": "Open Settings", "category": "Augment Balance"}, {"command": "augmentBalance.refreshBalance", "title": "Refresh Balance", "category": "Augment Balance"}]}, "scripts": {"vscode:prepublish": "npm run package", "compile": "webpack", "watch": "webpack --watch", "package": "webpack --mode production --devtool hidden-source-map", "compile-tests": "tsc -p . --outDir out", "watch-tests": "tsc -p . -w --outDir out", "pretest": "npm run compile-tests && npm run compile && npm run lint", "lint": "eslint src --ext ts", "test": "node ./out/test/runTest.js"}, "devDependencies": {"@types/node": "16.x", "@types/vscode": "^1.74.0", "@typescript-eslint/eslint-plugin": "^5.45.0", "@typescript-eslint/parser": "^5.45.0", "@vscode/test-electron": "^2.2.0", "eslint": "^8.28.0", "ts-loader": "^9.5.2", "typescript": "^4.9.4", "vsce": "^2.15.0", "webpack": "^5.100.0", "webpack-cli": "^6.0.1"}, "dependencies": {"axios": "^1.6.0"}}