<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🚀 Augment Code & Cursor 工具集 - 超炫酷展示</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --bg-primary: #0a0a0f;
            --bg-secondary: #1a1a2e;
            --bg-tertiary: #16213e;
            --accent-cyan: #00f5ff;
            --accent-purple: #8a2be2;
            --accent-gold: #ffd700;
            --accent-pink: #ff1493;
            --text-primary: #ffffff;
            --text-secondary: #b0b0b0;
            --text-glow: #00f5ff;
            --card-bg: rgba(26, 26, 46, 0.8);
            --glass-bg: rgba(255, 255, 255, 0.05);
            --glass-border: rgba(255, 255, 255, 0.1);
        }

        body {
            font-family: 'SF Pro Display', 'Helvetica Neue', 'Segoe UI', system-ui, sans-serif;
            background: var(--bg-primary);
            color: var(--text-primary);
            line-height: 1.6;
            min-height: 100vh;
            overflow-x: hidden;
            position: relative;
        }

        /* 动态星空背景 */
        .cosmic-bg {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -3;
            background:
                radial-gradient(circle at 20% 80%, var(--accent-purple) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, var(--accent-cyan) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, var(--accent-gold) 0%, transparent 50%),
                linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 50%, var(--bg-tertiary) 100%);
            animation: cosmicShift 20s ease-in-out infinite;
        }

        @keyframes cosmicShift {
            0%, 100% {
                filter: hue-rotate(0deg) brightness(1);
                transform: scale(1);
            }
            33% {
                filter: hue-rotate(120deg) brightness(1.2);
                transform: scale(1.05);
            }
            66% {
                filter: hue-rotate(240deg) brightness(0.8);
                transform: scale(0.95);
            }
        }

        /* 星星粒子 */
        .star {
            position: absolute;
            background: white;
            border-radius: 50%;
            animation: twinkle 3s ease-in-out infinite;
        }

        @keyframes twinkle {
            0%, 100% { opacity: 0.3; transform: scale(1); }
            50% { opacity: 1; transform: scale(1.2); }
        }

        /* 流星雨 */
        .meteor {
            position: absolute;
            width: 2px;
            height: 2px;
            background: var(--accent-cyan);
            border-radius: 50%;
            box-shadow:
                0 0 6px var(--accent-cyan),
                0 0 12px var(--accent-cyan),
                0 0 18px var(--accent-cyan);
            animation: meteorFall 4s linear infinite;
        }

        @keyframes meteorFall {
            0% {
                transform: translateX(-100px) translateY(-100px);
                opacity: 0;
            }
            10% { opacity: 1; }
            90% { opacity: 1; }
            100% {
                transform: translateX(calc(100vw + 100px)) translateY(calc(100vh + 100px));
                opacity: 0;
            }
        }

        /* 滚动进度条 */
        .scroll-progress {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            z-index: 1000;
            background: rgba(0, 245, 255, 0.1);
        }

        .progress-bar {
            height: 100%;
            background: linear-gradient(90deg, var(--accent-cyan), var(--accent-purple), var(--accent-gold));
            background-size: 200% 100%;
            width: 0%;
            animation: progressGlow 2s linear infinite;
            box-shadow: 0 0 10px var(--accent-cyan);
        }

        @keyframes progressGlow {
            0% { background-position: -200% 0; }
            100% { background-position: 200% 0; }
        }

        /* 主容器 */
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 20px;
            position: relative;
            z-index: 1;
        }

        /* 英雄区域 */
        .hero {
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .hero::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 800px;
            height: 800px;
            background: radial-gradient(circle, var(--accent-cyan) 0%, transparent 70%);
            transform: translate(-50%, -50%);
            opacity: 0.1;
            animation: heroGlow 6s ease-in-out infinite;
        }

        @keyframes heroGlow {
            0%, 100% { transform: translate(-50%, -50%) scale(1); opacity: 0.1; }
            50% { transform: translate(-50%, -50%) scale(1.2); opacity: 0.2; }
        }

        .hero-title {
            font-size: clamp(3rem, 8vw, 6rem);
            font-weight: 900;
            background: linear-gradient(45deg, var(--accent-cyan), var(--accent-purple), var(--accent-gold), var(--accent-pink));
            background-size: 400% 400%;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: gradientFlow 4s ease-in-out infinite, titleFloat 3s ease-in-out infinite;
            margin-bottom: 2rem;
            position: relative;
            z-index: 2;
            text-shadow: 0 0 50px rgba(0, 245, 255, 0.5);
        }

        @keyframes gradientFlow {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }

        @keyframes titleFloat {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        .hero-subtitle {
            font-size: clamp(1.2rem, 3vw, 1.8rem);
            color: var(--text-secondary);
            margin-bottom: 3rem;
            opacity: 0;
            animation: fadeInUp 1s ease-out 0.5s forwards;
            position: relative;
            z-index: 2;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* 工具网格 */
        .tools-section {
            padding: 100px 0;
            position: relative;
        }

        .section-title {
            font-size: clamp(2.5rem, 5vw, 4rem);
            font-weight: 800;
            text-align: center;
            margin-bottom: 4rem;
            background: linear-gradient(45deg, var(--accent-cyan), var(--accent-purple));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            opacity: 0;
            animation: slideInDown 1s ease-out forwards;
        }

        @keyframes slideInDown {
            from {
                opacity: 0;
                transform: translateY(-50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .tools-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 3rem;
            perspective: 1000px;
        }

        .tool-card {
            background: var(--card-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: 24px;
            padding: 2.5rem;
            position: relative;
            overflow: hidden;
            transition: all 0.6s cubic-bezier(0.23, 1, 0.32, 1);
            transform-style: preserve-3d;
            opacity: 0;
            transform: translateY(100px);
            animation: cardReveal 0.8s ease-out forwards;
        }

        .tool-card:nth-child(1) { animation-delay: 0.1s; }
        .tool-card:nth-child(2) { animation-delay: 0.2s; }
        .tool-card:nth-child(3) { animation-delay: 0.3s; }
        .tool-card:nth-child(4) { animation-delay: 0.4s; }
        .tool-card:nth-child(5) { animation-delay: 0.5s; }

        @keyframes cardReveal {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .tool-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(0, 245, 255, 0.1), transparent);
            transition: left 0.8s ease;
        }

        .tool-card::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, var(--accent-cyan), var(--accent-purple), var(--accent-gold));
            background-size: 200% 100%;
            transform: scaleX(0);
            transition: transform 0.5s ease;
            animation: borderGlow 3s linear infinite;
        }

        @keyframes borderGlow {
            0% { background-position: -200% 0; }
            100% { background-position: 200% 0; }
        }

        .tool-card:hover {
            transform: translateY(-20px) rotateX(10deg) rotateY(10deg);
            box-shadow:
                0 30px 60px rgba(0, 245, 255, 0.2),
                0 0 80px rgba(138, 43, 226, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
        }

        .tool-card:hover::before {
            left: 100%;
        }

        .tool-card:hover::after {
            transform: scaleX(1);
        }

        .tool-header {
            display: flex;
            align-items: center;
            margin-bottom: 2rem;
        }

        .tool-icon {
            width: 70px;
            height: 70px;
            background: linear-gradient(135deg, var(--accent-cyan), var(--accent-purple));
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            margin-right: 1.5rem;
            position: relative;
            overflow: hidden;
            animation: iconFloat 4s ease-in-out infinite;
            box-shadow:
                0 10px 30px rgba(0, 245, 255, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
        }

        .tool-icon::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transform: rotate(45deg);
            animation: iconShine 3s linear infinite;
        }

        @keyframes iconFloat {
            0%, 100% {
                transform: translateY(0) scale(1);
                box-shadow: 0 10px 30px rgba(0, 245, 255, 0.3);
            }
            50% {
                transform: translateY(-5px) scale(1.05);
                box-shadow: 0 15px 40px rgba(0, 245, 255, 0.4);
            }
        }

        @keyframes iconShine {
            0% { transform: translateX(-200%) translateY(-200%) rotate(45deg); }
            100% { transform: translateX(200%) translateY(200%) rotate(45deg); }
        }

        .tool-title {
            font-size: 1.8rem;
            font-weight: 700;
            background: linear-gradient(45deg, var(--text-primary), var(--accent-cyan));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 0.5rem;
        }

        .tool-subtitle {
            font-size: 1rem;
            color: var(--text-secondary);
            font-family: 'SF Mono', 'Monaco', 'Cascadia Code', monospace;
            opacity: 0.7;
        }

        .tool-description {
            color: var(--text-secondary);
            margin-bottom: 2rem;
            line-height: 1.7;
            font-size: 1.1rem;
        }

        .features-list {
            list-style: none;
            margin-bottom: 2rem;
        }

        .features-list li {
            padding: 0.8rem 0;
            color: var(--text-secondary);
            position: relative;
            padding-left: 2rem;
            transition: all 0.3s ease;
            opacity: 0;
            transform: translateX(-20px);
            animation: featureSlideIn 0.6s ease-out forwards;
        }

        .features-list li:nth-child(1) { animation-delay: 0.1s; }
        .features-list li:nth-child(2) { animation-delay: 0.2s; }
        .features-list li:nth-child(3) { animation-delay: 0.3s; }
        .features-list li:nth-child(4) { animation-delay: 0.4s; }
        .features-list li:nth-child(5) { animation-delay: 0.5s; }
        .features-list li:nth-child(6) { animation-delay: 0.6s; }

        @keyframes featureSlideIn {
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        .features-list li:hover {
            color: var(--accent-cyan);
            transform: translateX(10px);
            text-shadow: 0 0 10px rgba(0, 245, 255, 0.5);
        }

        .features-list li::before {
            content: '✨';
            position: absolute;
            left: 0;
            color: var(--accent-gold);
            animation: sparkle 2s ease-in-out infinite;
        }

        @keyframes sparkle {
            0%, 100% { transform: scale(1) rotate(0deg); }
            50% { transform: scale(1.2) rotate(180deg); }
        }

        .tool-status {
            display: inline-block;
            padding: 0.8rem 1.5rem;
            border-radius: 30px;
            font-size: 0.9rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
            position: relative;
            overflow: hidden;
            backdrop-filter: blur(10px);
            animation: statusGlow 3s ease-in-out infinite;
        }

        @keyframes statusGlow {
            0%, 100% {
                transform: scale(1);
                filter: brightness(1);
            }
            50% {
                transform: scale(1.05);
                filter: brightness(1.2);
            }
        }

        .status-core {
            background: linear-gradient(45deg, rgba(0, 245, 255, 0.2), rgba(0, 245, 255, 0.1));
            color: var(--accent-cyan);
            border: 1px solid var(--accent-cyan);
            box-shadow: 0 0 20px rgba(0, 245, 255, 0.3);
        }

        .status-gui {
            background: linear-gradient(45deg, rgba(255, 215, 0, 0.2), rgba(255, 215, 0, 0.1));
            color: var(--accent-gold);
            border: 1px solid var(--accent-gold);
            box-shadow: 0 0 20px rgba(255, 215, 0, 0.3);
        }

        .status-utility {
            background: linear-gradient(45deg, rgba(138, 43, 226, 0.2), rgba(138, 43, 226, 0.1));
            color: var(--accent-purple);
            border: 1px solid var(--accent-purple);
            box-shadow: 0 0 20px rgba(138, 43, 226, 0.3);
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .hero-title {
                font-size: 3rem;
            }

            .hero-subtitle {
                font-size: 1.2rem;
            }

            .tools-grid {
                grid-template-columns: 1fr;
                gap: 2rem;
            }

            .tool-card {
                padding: 2rem;
            }

            .container {
                padding: 0 15px;
            }
        }

    </style>
</head>
<body>
    <!-- 宇宙背景 -->
    <div class="cosmic-bg"></div>

    <!-- 滚动进度条 -->
    <div class="scroll-progress">
        <div class="progress-bar" id="progressBar"></div>
    </div>

    <div class="container">
        <!-- 英雄区域 -->
        <section class="hero">
            <h1 class="hero-title">🚀 Augment Code & Cursor 工具集</h1>
            <p class="hero-subtitle">专业的账户管理、网络优化和连接测试解决方案</p>
        </section>

        <!-- 工具展示区域 -->
        <section class="tools-section">
            <h2 class="section-title">✨ 核心工具</h2>
            <div class="tools-grid">
                <div class="tool-card">
                    <div class="tool-header">
                        <div class="tool-icon">📊</div>
                        <div>
                            <div class="tool-title">Account Info Fetcher</div>
                            <div class="tool-subtitle">account_info_fetcher.py</div>
                        </div>
                    </div>
                    <div class="tool-status status-core">核心工具</div>
                    <p class="tool-description">
                        获取Augment Code账号的详细信息，包括订阅状态、使用量统计和剩余天数计算。
                    </p>
                    <ul class="features-list">
                        <li>通过portal token获取订阅信息</li>
                        <li>显示邮箱、计划类型、有效性状态</li>
                        <li>计算使用量和剩余天数</li>
                        <li>支持北京时间转换</li>
                        <li>获取账户余额信息</li>
                    </ul>
                </div>

                <div class="tool-card">
                    <div class="tool-header">
                        <div class="tool-icon">🔑</div>
                        <div>
                            <div class="tool-title">Subscription Fetcher</div>
                            <div class="tool-subtitle">subscription_fetcher.py</div>
                        </div>
                    </div>
                    <div class="tool-status status-core">前置工具</div>
                    <p class="tool-description">
                        从Augment Code获取subscription信息并提取portal token，为账户信息获取提供必要的认证。
                    </p>
                    <ul class="features-list">
                        <li>使用session token访问API</li>
                        <li>提取portalUrl中的token参数</li>
                        <li>为account_info_fetcher.py提供token</li>
                        <li>支持完整响应数据展示</li>
                    </ul>
                </div>

                <div class="tool-card">
                    <div class="tool-header">
                        <div class="tool-icon">🖥️</div>
                        <div>
                            <div class="tool-title">Cursor Manager GUI</div>
                            <div class="tool-subtitle">test.py</div>
                        </div>
                    </div>
                    <div class="tool-status status-gui">图形界面</div>
                    <p class="tool-description">
                        完整的Cursor账户管理图形界面应用，提供现代化的暗色主题和丰富的交互功能。
                    </p>
                    <ul class="features-list">
                        <li>现代化的暗色主题GUI界面</li>
                        <li>账户信息管理和显示</li>
                        <li>额度查询和监控</li>
                        <li>账户切换和认证管理</li>
                        <li>批量操作功能</li>
                        <li>动画效果和用户体验优化</li>
                    </ul>
                </div>

                <div class="tool-card">
                    <div class="tool-header">
                        <div class="tool-icon">🔧</div>
                        <div>
                            <div class="tool-title">Network Optimizer</div>
                            <div class="tool-subtitle">yaugment_optimizer.py</div>
                        </div>
                    </div>
                    <div class="tool-status status-utility">优化工具</div>
                    <p class="tool-description">
                        测速并优化augmentcode.com的连接速度，支持多种代理配置和自动hosts文件优化。
                    </p>
                    <ul class="features-list">
                        <li>并发测试多个API域名速度</li>
                        <li>支持代理配置（v2rayN、Clash等）</li>
                        <li>自动更新hosts文件优化连接</li>
                        <li>DNS缓存清理</li>
                        <li>管理员权限检查</li>
                    </ul>
                </div>

                <div class="tool-card">
                    <div class="tool-header">
                        <div class="tool-icon">🌐</div>
                        <div>
                            <div class="tool-title">Connection Tester</div>
                            <div class="tool-subtitle">test_curl.py</div>
                        </div>
                    </div>
                    <div class="tool-status status-utility">调试工具</div>
                    <p class="tool-description">
                        测试curl连接到Augment Code API域名的调试脚本，提供详细的连接诊断信息。
                    </p>
                    <ul class="features-list">
                        <li>基础连通性测试</li>
                        <li>详细诊断测试</li>
                        <li>DNS解析测试</li>
                        <li>支持多个域名测试</li>
                        <li>连接时间和状态码显示</li>
                    </ul>
                </div>
            </div>
        </section>
    </div>

    <script>
        // 创建星空背景
        function createStars() {
            const cosmicBg = document.querySelector('.cosmic-bg');
            const starCount = 100;

            for (let i = 0; i < starCount; i++) {
                const star = document.createElement('div');
                star.className = 'star';
                star.style.left = Math.random() * 100 + '%';
                star.style.top = Math.random() * 100 + '%';
                star.style.width = Math.random() * 3 + 1 + 'px';
                star.style.height = star.style.width;
                star.style.animationDelay = Math.random() * 3 + 's';
                star.style.animationDuration = (Math.random() * 2 + 2) + 's';
                cosmicBg.appendChild(star);
            }
        }

        // 创建流星雨
        function createMeteors() {
            setInterval(() => {
                const meteor = document.createElement('div');
                meteor.className = 'meteor';
                meteor.style.left = Math.random() * 100 + '%';
                meteor.style.top = Math.random() * 50 + '%';
                meteor.style.animationDuration = (Math.random() * 2 + 2) + 's';
                document.body.appendChild(meteor);

                setTimeout(() => {
                    meteor.remove();
                }, 4000);
            }, 3000);
        }

        // 滚动进度条
        function updateScrollProgress() {
            const scrollTop = window.pageYOffset;
            const docHeight = document.body.offsetHeight - window.innerHeight;
            const scrollPercent = (scrollTop / docHeight) * 100;
            document.getElementById('progressBar').style.width = scrollPercent + '%';
        }

        // 视差滚动效果
        function handleParallax() {
            const scrolled = window.pageYOffset;
            const parallaxElements = document.querySelectorAll('.tool-card');

            parallaxElements.forEach((element, index) => {
                const speed = 0.1 + (index % 3) * 0.05;
                const yPos = -(scrolled * speed);
                element.style.transform = `translateY(${yPos}px)`;
            });
        }

        // 鼠标跟随效果
        function handleMouseMove(e) {
            const mouseX = e.clientX / window.innerWidth;
            const mouseY = e.clientY / window.innerHeight;

            // 背景视差
            const cosmicBg = document.querySelector('.cosmic-bg');
            const x = (mouseX - 0.5) * 20;
            const y = (mouseY - 0.5) * 20;
            cosmicBg.style.transform = `translate(${x}px, ${y}px)`;
        }

        // 滚动触发动画
        function handleScrollAnimations() {
            const cards = document.querySelectorAll('.tool-card');
            const triggerBottom = window.innerHeight * 0.8;

            cards.forEach(card => {
                const cardTop = card.getBoundingClientRect().top;

                if (cardTop < triggerBottom) {
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }
            });
        }

        // 粒子爆炸效果
        function createParticleExplosion(element) {
            const rect = element.getBoundingClientRect();
            const centerX = rect.left + rect.width / 2;
            const centerY = rect.top + rect.height / 2;

            for (let i = 0; i < 12; i++) {
                const particle = document.createElement('div');
                particle.style.position = 'fixed';
                particle.style.left = centerX + 'px';
                particle.style.top = centerY + 'px';
                particle.style.width = '6px';
                particle.style.height = '6px';
                particle.style.background = 'var(--accent-cyan)';
                particle.style.borderRadius = '50%';
                particle.style.pointerEvents = 'none';
                particle.style.zIndex = '9999';
                particle.style.boxShadow = '0 0 10px var(--accent-cyan)';

                const angle = (Math.PI * 2 * i) / 12;
                const velocity = 80 + Math.random() * 40;
                const vx = Math.cos(angle) * velocity;
                const vy = Math.sin(angle) * velocity;

                document.body.appendChild(particle);

                let x = 0, y = 0, opacity = 1;
                const animate = () => {
                    x += vx * 0.02;
                    y += vy * 0.02;
                    opacity -= 0.02;

                    particle.style.transform = `translate(${x}px, ${y}px)`;
                    particle.style.opacity = opacity;

                    if (opacity > 0) {
                        requestAnimationFrame(animate);
                    } else {
                        particle.remove();
                    }
                };
                animate();
            }
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', () => {
            createStars();
            createMeteors();

            // 添加事件监听器
            window.addEventListener('scroll', () => {
                updateScrollProgress();
                handleParallax();
                handleScrollAnimations();
            });

            window.addEventListener('mousemove', handleMouseMove);

            // 为工具卡片添加悬停效果
            const toolCards = document.querySelectorAll('.tool-card');
            toolCards.forEach(card => {
                card.addEventListener('mouseenter', () => {
                    createParticleExplosion(card);
                });
            });

            // 初始动画检查
            handleScrollAnimations();
        });
    </script>
</body>
</html>
            overflow: hidden;
        }

        body:not(.loaded) .container {
            opacity: 0;
            transform: scale(0.95);
        }

        body.loaded .container {
            opacity: 1;
            transform: scale(1);
            transition: all 1s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        }

        /* 滚动方向相关动画 */
        body[data-scroll-direction="down"] .parallax-element {
            animation: scrollDown 0.3s ease-out;
        }

        body[data-scroll-direction="up"] .parallax-element {
            animation: scrollUp 0.3s ease-out;
        }

        @keyframes scrollDown {
            from { transform: translateY(-5px); }
            to { transform: translateY(0); }
        }

        @keyframes scrollUp {
            from { transform: translateY(5px); }
            to { transform: translateY(0); }
        }

        @media (max-width: 768px) {
            .header h1 {
                font-size: 2.5rem;
            }

            .tools-grid {
                grid-template-columns: 1fr;
                gap: 25px;
            }

            .workflow-steps {
                grid-template-columns: 1fr;
            }

            .container {
                padding: 15px;
            }

            /* 移动端减少视差效果以提升性能 */
            .parallax-element {
                transform: none !important;
            }

            .parallax-decoration {
                display: none;
            }
        }

        .scroll-indicator {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 6px;
            background: rgba(43, 157, 124, 0.2);
            z-index: 1000;
            overflow: hidden;
        }

        .scroll-progress {
            height: 100%;
            background: linear-gradient(90deg, var(--accent), var(--gold), var(--neon-blue), var(--accent));
            background-size: 200% 100%;
            width: 0%;
            transition: width 0.1s ease;
            animation: progressShimmer 2s linear infinite;
            box-shadow: 0 0 20px rgba(43, 157, 124, 0.5);
        }

        @keyframes progressShimmer {
            0% { background-position: -200% 0; }
            100% { background-position: 200% 0; }
        }

        /* 鼠标跟随光效 */
        .cursor-glow {
            position: fixed;
            width: 20px;
            height: 20px;
            background: radial-gradient(circle, var(--accent) 0%, transparent 70%);
            border-radius: 50%;
            pointer-events: none;
            z-index: 9999;
            mix-blend-mode: screen;
            transition: transform 0.1s ease;
        }

        /* 浮动装饰元素 */
        .floating-element {
            position: absolute;
            pointer-events: none;
            opacity: 0.1;
            animation: floatAround 20s linear infinite;
            will-change: transform;
        }

        @keyframes floatAround {
            0% { transform: translateX(0) translateY(0) rotate(0deg); }
            25% { transform: translateX(100px) translateY(-50px) rotate(90deg); }
            50% { transform: translateX(50px) translateY(-100px) rotate(180deg); }
            75% { transform: translateX(-50px) translateY(-50px) rotate(270deg); }
            100% { transform: translateX(0) translateY(0) rotate(360deg); }
        }

        /* 视差滚动层 */
        .parallax-layer {
            position: absolute;
            width: 100%;
            height: 100%;
            will-change: transform;
        }

        .parallax-bg {
            position: fixed;
            top: 0;
            left: 0;
            width: 120%;
            height: 120%;
            z-index: -2;
            background: radial-gradient(circle at 20% 80%, rgba(43, 157, 124, 0.1) 0%, transparent 50%),
                        radial-gradient(circle at 80% 20%, rgba(255, 215, 0, 0.1) 0%, transparent 50%),
                        radial-gradient(circle at 40% 40%, rgba(0, 255, 255, 0.05) 0%, transparent 50%);
            animation: parallaxBgMove 30s ease-in-out infinite;
        }

        @keyframes parallaxBgMove {
            0%, 100% { transform: translate(0, 0) rotate(0deg); }
            33% { transform: translate(-20px, -30px) rotate(1deg); }
            66% { transform: translate(20px, -20px) rotate(-1deg); }
        }

        /* 滚动触发的动画类 */
        .scroll-reveal {
            opacity: 0;
            transform: translateY(100px) scale(0.8);
            transition: all 1.2s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        }

        .scroll-reveal.revealed {
            opacity: 1;
            transform: translateY(0) scale(1);
        }

        .scroll-reveal-left {
            opacity: 0;
            transform: translateX(-100px) rotateY(-30deg);
            transition: all 1s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        }

        .scroll-reveal-left.revealed {
            opacity: 1;
            transform: translateX(0) rotateY(0deg);
        }

        .scroll-reveal-right {
            opacity: 0;
            transform: translateX(100px) rotateY(30deg);
            transition: all 1s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        }

        .scroll-reveal-right.revealed {
            opacity: 1;
            transform: translateX(0) rotateY(0deg);
        }

        .scroll-reveal-scale {
            opacity: 0;
            transform: scale(0.3) rotate(45deg);
            transition: all 1.5s cubic-bezier(0.68, -0.55, 0.265, 1.55);
        }

        .scroll-reveal-scale.revealed {
            opacity: 1;
            transform: scale(1) rotate(0deg);
        }

        /* 视差元素 */
        .parallax-element {
            will-change: transform;
            transition: transform 0.1s ease-out;
        }

        /* 深度层次视差 */
        .depth-1 { transform: translateZ(0); }
        .depth-2 { transform: translateZ(-100px) scale(1.1); }
        .depth-3 { transform: translateZ(-200px) scale(1.2); }
        .depth-4 { transform: translateZ(-300px) scale(1.3); }
    </style>
</head>
<body>
    <!-- 动态背景 -->
    <div class="parallax-bg"></div>
    <div class="particles-bg" id="particlesBg"></div>
    <div class="cursor-glow" id="cursorGlow"></div>

    <!-- 滚动进度条 -->
    <div class="scroll-indicator">
        <div class="scroll-progress" id="scrollProgress"></div>
    </div>

    <!-- 视差装饰元素 -->
    <div class="parallax-decoration">🚀</div>
    <div class="parallax-decoration">⚡</div>
    <div class="parallax-decoration">💻</div>
    <div class="parallax-decoration">🌟</div>

    <div class="container parallax-element depth-1">
        <header class="header parallax-element depth-2 scroll-reveal">
            <h1 class="typewriter">🚀 Augment Code & Cursor 工具集</h1>
            <p class="scroll-reveal-scale">专业的账户管理、网络优化和连接测试解决方案</p>
            <!-- 浮动装饰元素 -->
            <div class="floating-element parallax-element" style="top: 10%; left: 10%; font-size: 2rem;" data-speed="0.5">⚡</div>
            <div class="floating-element parallax-element" style="top: 20%; right: 15%; font-size: 1.5rem;" data-speed="0.3">🔧</div>
            <div class="floating-element parallax-element" style="bottom: 30%; left: 20%; font-size: 1.8rem;" data-speed="0.7">💻</div>
            <div class="floating-element parallax-element" style="bottom: 10%; right: 10%; font-size: 2.2rem;" data-speed="0.4">🌟</div>
        </header>

        <div class="tools-grid parallax-element depth-1">
            <div class="tool-card scroll-reveal-left parallax-element" data-speed="0.2">
                <div class="tool-header">
                    <div class="tool-icon">📊</div>
                    <div>
                        <div class="tool-title">Account Info Fetcher</div>
                        <div class="tool-subtitle">account_info_fetcher.py</div>
                    </div>
                </div>
                <div class="tool-status status-active">核心工具</div>
                <p class="tool-description">
                    获取Augment Code账号的详细信息，包括订阅状态、使用量统计和剩余天数计算。
                </p>
                <ul class="features-list">
                    <li>通过portal token获取订阅信息</li>
                    <li>显示邮箱、计划类型、有效性状态</li>
                    <li>计算使用量和剩余天数</li>
                    <li>支持北京时间转换</li>
                    <li>获取账户余额信息</li>
                </ul>
            </div>

            <div class="tool-card scroll-reveal-right parallax-element" data-speed="0.3">
                <div class="tool-header">
                    <div class="tool-icon">🔑</div>
                    <div>
                        <div class="tool-title">Subscription Fetcher</div>
                        <div class="tool-subtitle">subscription_fetcher.py</div>
                    </div>
                </div>
                <div class="tool-status status-active">前置工具</div>
                <p class="tool-description">
                    从Augment Code获取subscription信息并提取portal token，为账户信息获取提供必要的认证。
                </p>
                <ul class="features-list">
                    <li>使用session token访问API</li>
                    <li>提取portalUrl中的token参数</li>
                    <li>为account_info_fetcher.py提供token</li>
                    <li>支持完整响应数据展示</li>
                </ul>
            </div>

            <div class="tool-card scroll-reveal-scale parallax-element" data-speed="0.4">
                <div class="tool-header">
                    <div class="tool-icon">🖥️</div>
                    <div>
                        <div class="tool-title">Cursor Manager GUI</div>
                        <div class="tool-subtitle">test.py</div>
                    </div>
                </div>
                <div class="tool-status status-gui">图形界面</div>
                <p class="tool-description">
                    完整的Cursor账户管理图形界面应用，提供现代化的暗色主题和丰富的交互功能。
                </p>
                <ul class="features-list">
                    <li>现代化的暗色主题GUI界面</li>
                    <li>账户信息管理和显示</li>
                    <li>额度查询和监控</li>
                    <li>账户切换和认证管理</li>
                    <li>批量操作功能</li>
                    <li>动画效果和用户体验优化</li>
                </ul>
            </div>

            <div class="tool-card scroll-reveal-left parallax-element" data-speed="0.5">
                <div class="tool-header">
                    <div class="tool-icon">🔧</div>
                    <div>
                        <div class="tool-title">Network Optimizer</div>
                        <div class="tool-subtitle">yaugment_optimizer.py</div>
                    </div>
                </div>
                <div class="tool-status status-utility">优化工具</div>
                <p class="tool-description">
                    测速并优化augmentcode.com的连接速度，支持多种代理配置和自动hosts文件优化。
                </p>
                <ul class="features-list">
                    <li>并发测试多个API域名速度</li>
                    <li>支持代理配置（v2rayN、Clash等）</li>
                    <li>自动更新hosts文件优化连接</li>
                    <li>DNS缓存清理</li>
                    <li>管理员权限检查</li>
                </ul>
            </div>

            <div class="tool-card scroll-reveal-right parallax-element" data-speed="0.6">
                <div class="tool-header">
                    <div class="tool-icon">🌐</div>
                    <div>
                        <div class="tool-title">Connection Tester</div>
                        <div class="tool-subtitle">test_curl.py</div>
                    </div>
                </div>
                <div class="tool-status status-utility">调试工具</div>
                <p class="tool-description">
                    测试curl连接到Augment Code API域名的调试脚本，提供详细的连接诊断信息。
                </p>
                <ul class="features-list">
                    <li>基础连通性测试</li>
                    <li>详细诊断测试</li>
                    <li>DNS解析测试</li>
                    <li>支持多个域名测试</li>
                    <li>连接时间和状态码显示</li>
                </ul>
            </div>
        </div>

        <div class="workflow-section scroll-reveal parallax-element depth-2" data-speed="0.3">
            <h2 class="workflow-title scroll-reveal-scale">🔄 使用工作流程</h2>
            <div class="workflow-steps">
                <div class="workflow-step scroll-reveal-left parallax-element" data-speed="0.1">
                    <div class="step-number">1</div>
                    <div class="step-title">获取Token</div>
                    <div class="step-description">运行 subscription_fetcher.py 获取portal token</div>
                </div>
                <div class="workflow-step scroll-reveal-right parallax-element" data-speed="0.2">
                    <div class="step-number">2</div>
                    <div class="step-title">查看账户信息</div>
                    <div class="step-description">使用 account_info_fetcher.py 获取详细账户信息</div>
                </div>
                <div class="workflow-step scroll-reveal-left parallax-element" data-speed="0.3">
                    <div class="step-number">3</div>
                    <div class="step-title">网络优化</div>
                    <div class="step-description">运行 yaugment_optimizer.py 优化网络连接</div>
                </div>
                <div class="workflow-step scroll-reveal-right parallax-element" data-speed="0.4">
                    <div class="step-number">4</div>
                    <div class="step-title">Cursor管理</div>
                    <div class="step-description">使用 test.py 进行图形化账户管理</div>
                </div>
            </div>
        </div>

        <footer class="footer scroll-reveal parallax-element depth-3" data-speed="0.1">
            <p class="scroll-reveal-scale">🛠️ 为 Augment Code 和 Cursor 用户提供完整的管理解决方案</p>
            <div class="tech-stack">
                <span class="tech-badge scroll-reveal-left">Python 3.x</span>
                <span class="tech-badge scroll-reveal-right">PyQt6</span>
                <span class="tech-badge scroll-reveal-left">Requests</span>
                <span class="tech-badge scroll-reveal-right">SQLite3</span>
                <span class="tech-badge scroll-reveal-left">Threading</span>
                <span class="tech-badge scroll-reveal-right">AsyncIO</span>
            </div>
        </footer>
    </div>

    <script>
        // 滚动进度指示器和视差效果
        let ticking = false;

        function updateParallax() {
            const scrollTop = window.pageYOffset;
            const docHeight = document.body.offsetHeight - window.innerHeight;
            const scrollPercent = (scrollTop / docHeight) * 100;

            // 更新进度条
            document.getElementById('scrollProgress').style.width = scrollPercent + '%';

            // 视差效果
            const parallaxElements = document.querySelectorAll('.parallax-element');
            parallaxElements.forEach(element => {
                const speed = element.dataset.speed || 0.5;
                const yPos = -(scrollTop * speed);
                element.style.transform = `translateY(${yPos}px)`;
            });

            // 视差装饰元素
            const decorations = document.querySelectorAll('.parallax-decoration');
            decorations.forEach((decoration, index) => {
                const speed = 0.2 + (index * 0.1);
                const yPos = -(scrollTop * speed);
                const rotation = scrollTop * 0.1;
                decoration.style.transform = `translateY(${yPos}px) rotate(${rotation}deg)`;
            });

            ticking = false;
        }

        function requestTick() {
            if (!ticking) {
                requestAnimationFrame(updateParallax);
                ticking = true;
            }
        }

        window.addEventListener('scroll', requestTick);

        // 鼠标跟随光效
        document.addEventListener('mousemove', (e) => {
            const cursorGlow = document.getElementById('cursorGlow');
            cursorGlow.style.left = e.clientX - 10 + 'px';
            cursorGlow.style.top = e.clientY - 10 + 'px';
        });

        // 创建粒子效果
        function createParticles() {
            const particlesBg = document.getElementById('particlesBg');
            const particleCount = 50;

            for (let i = 0; i < particleCount; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';
                particle.style.left = Math.random() * 100 + '%';
                particle.style.top = Math.random() * 100 + '%';
                particle.style.width = Math.random() * 4 + 2 + 'px';
                particle.style.height = particle.style.width;
                particle.style.animationDelay = Math.random() * 6 + 's';
                particle.style.animationDuration = (Math.random() * 3 + 3) + 's';
                particlesBg.appendChild(particle);
            }
        }

        // 创建流星效果
        function createShootingStars() {
            setInterval(() => {
                const star = document.createElement('div');
                star.className = 'shooting-star';
                star.style.left = Math.random() * 100 + '%';
                star.style.top = Math.random() * 50 + '%';
                star.style.animationDuration = (Math.random() * 2 + 1) + 's';
                document.body.appendChild(star);

                setTimeout(() => {
                    star.remove();
                }, 3000);
            }, 2000);
        }

        // 高级滚动触发动画观察器
        const observerOptions = {
            threshold: 0.15,
            rootMargin: '0px 0px -100px 0px'
        };

        const revealObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    // 根据不同的类添加不同的动画
                    if (entry.target.classList.contains('scroll-reveal')) {
                        entry.target.classList.add('revealed');
                    }
                    if (entry.target.classList.contains('scroll-reveal-left')) {
                        entry.target.classList.add('revealed');
                    }
                    if (entry.target.classList.contains('scroll-reveal-right')) {
                        entry.target.classList.add('revealed');
                    }
                    if (entry.target.classList.contains('scroll-reveal-scale')) {
                        entry.target.classList.add('revealed');
                    }

                    // 添加随机延迟的闪烁效果
                    setTimeout(() => {
                        entry.target.style.animation = 'cardGlow 0.8s ease-in-out';
                    }, Math.random() * 1500);

                    // 停止观察已经显示的元素
                    revealObserver.unobserve(entry.target);
                }
            });
        }, observerOptions);

        // 视差滚动效果增强
        function enhancedParallaxEffect() {
            const scrolled = window.pageYOffset;
            const rate = scrolled * -0.5;

            // 背景视差
            const parallaxBg = document.querySelector('.parallax-bg');
            if (parallaxBg) {
                parallaxBg.style.transform = `translate3d(0, ${rate}px, 0)`;
            }

            // 工具卡片的微妙视差
            const toolCards = document.querySelectorAll('.tool-card');
            toolCards.forEach((card, index) => {
                const speed = 0.1 + (index % 3) * 0.05;
                const yPos = scrolled * speed;
                card.style.transform += ` translateZ(${yPos * 0.1}px)`;
            });
        }

        // 鼠标移动视差效果
        document.addEventListener('mousemove', (e) => {
            const mouseX = e.clientX / window.innerWidth;
            const mouseY = e.clientY / window.innerHeight;

            // 浮动元素跟随鼠标
            const floatingElements = document.querySelectorAll('.floating-element');
            floatingElements.forEach((element, index) => {
                const speed = 0.02 + (index * 0.01);
                const x = (mouseX - 0.5) * 50 * speed;
                const y = (mouseY - 0.5) * 50 * speed;
                element.style.transform += ` translate(${x}px, ${y}px)`;
            });

            // 视差装饰元素
            const decorations = document.querySelectorAll('.parallax-decoration');
            decorations.forEach((decoration, index) => {
                const speed = 0.03 + (index * 0.01);
                const x = (mouseX - 0.5) * 30 * speed;
                const y = (mouseY - 0.5) * 30 * speed;
                decoration.style.transform += ` translate(${x}px, ${y}px)`;
            });
        });

        // 卡片发光动画
        const style = document.createElement('style');
        style.textContent = `
            @keyframes cardGlow {
                0%, 100% { box-shadow: 0 20px 40px rgba(43, 157, 124, 0.3); }
                50% { box-shadow: 0 20px 40px rgba(43, 157, 124, 0.6), 0 0 50px rgba(43, 157, 124, 0.4); }
            }
        `;
        document.head.appendChild(style);

        // 打字机效果
        function typeWriter(element, text, speed = 100) {
            let i = 0;
            element.innerHTML = '';
            element.style.borderRight = '3px solid var(--accent)';

            function type() {
                if (i < text.length) {
                    element.innerHTML += text.charAt(i);
                    i++;
                    setTimeout(type, speed);
                } else {
                    // 闪烁光标效果
                    setInterval(() => {
                        element.style.borderRight = element.style.borderRight === 'none' ?
                            '3px solid var(--accent)' : 'none';
                    }, 750);
                }
            }
            type();
        }

        // 初始化所有效果
        document.addEventListener('DOMContentLoaded', () => {
            // 创建粒子和流星
            createParticles();
            createShootingStars();

            // 打字机效果
            const title = document.querySelector('.typewriter');
            if (title) {
                const originalText = title.textContent;
                typeWriter(title, originalText, 80);
            }

            // 初始化所有滚动触发元素
            const revealElements = document.querySelectorAll('.scroll-reveal, .scroll-reveal-left, .scroll-reveal-right, .scroll-reveal-scale');
            revealElements.forEach((element, index) => {
                revealObserver.observe(element);

                // 为tech-badge添加延迟动画
                if (element.classList.contains('tech-badge')) {
                    element.style.transitionDelay = `${index * 0.1}s`;
                }
            });

            // 添加鼠标悬停时的粒子爆炸效果
            const interactiveElements = document.querySelectorAll('.tool-card, .workflow-step, .tech-badge');
            interactiveElements.forEach(element => {
                element.addEventListener('mouseenter', () => {
                    createParticleExplosion(element);
                });
            });

            // 启动增强视差效果
            window.addEventListener('scroll', enhancedParallaxEffect);

            // 添加滚动方向检测
            let lastScrollTop = 0;
            window.addEventListener('scroll', () => {
                const scrollTop = window.pageYOffset;
                const scrollDirection = scrollTop > lastScrollTop ? 'down' : 'up';

                // 根据滚动方向调整动画
                document.body.setAttribute('data-scroll-direction', scrollDirection);
                lastScrollTop = scrollTop;
            });

            // 添加页面加载完成后的整体动画
            setTimeout(() => {
                document.body.classList.add('loaded');
            }, 500);
        });

        // 粒子爆炸效果
        function createParticleExplosion(element) {
            const rect = element.getBoundingClientRect();
            const centerX = rect.left + rect.width / 2;
            const centerY = rect.top + rect.height / 2;

            for (let i = 0; i < 10; i++) {
                const particle = document.createElement('div');
                particle.style.position = 'fixed';
                particle.style.left = centerX + 'px';
                particle.style.top = centerY + 'px';
                particle.style.width = '4px';
                particle.style.height = '4px';
                particle.style.background = 'var(--accent)';
                particle.style.borderRadius = '50%';
                particle.style.pointerEvents = 'none';
                particle.style.zIndex = '9999';

                const angle = (Math.PI * 2 * i) / 10;
                const velocity = 50 + Math.random() * 50;
                const vx = Math.cos(angle) * velocity;
                const vy = Math.sin(angle) * velocity;

                document.body.appendChild(particle);

                let x = 0, y = 0;
                const animate = () => {
                    x += vx * 0.02;
                    y += vy * 0.02;
                    particle.style.transform = `translate(${x}px, ${y}px)`;
                    particle.style.opacity = Math.max(0, 1 - Math.abs(x) / 100);

                    if (Math.abs(x) < 100) {
                        requestAnimationFrame(animate);
                    } else {
                        particle.remove();
                    }
                };
                animate();
            }
        }

        // 平滑滚动
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    </script>
</body>
</html>
