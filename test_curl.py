#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试curl连接的调试脚本
"""

import subprocess
import platform

def test_single_domain(domain="d1.api.augmentcode.com"):
    """测试单个域名连接"""
    print(f"测试域名: {domain}")
    print("=" * 50)
    
    system = platform.system().lower()
    
    # 基础curl命令测试
    print("1. 基础连通性测试:")
    curl_cmd = [
        'curl', f'https://{domain}/',
        '--head',
        '--insecure',
        '--max-time', '10',
        '--connect-timeout', '5',
        '--show-error',
        '--write-out', '状态码: %{http_code}, 连接时间: %{time_connect}s, 总时间: %{time_total}s, 真实IP: %{remote_ip}\n'
    ]
    
    try:
        result = subprocess.run(curl_cmd, capture_output=True, text=True, timeout=15)
        print(f"返回码: {result.returncode}")
        print(f"输出: {result.stdout}")
        if result.stderr:
            print(f"错误: {result.stderr}")
    except subprocess.TimeoutExpired:
        print("❌ 超时")
    except Exception as e:
        print(f"❌ 异常: {e}")
    
    print("\n2. 详细诊断测试:")
    # 详细诊断命令
    curl_cmd_verbose = [
        'curl', f'https://{domain}/',
        '--head',
        '--verbose',
        '--insecure',
        '--max-time', '10'
    ]
    
    try:
        result = subprocess.run(curl_cmd_verbose, capture_output=True, text=True, timeout=15)
        print(f"返回码: {result.returncode}")
        print("详细输出:")
        print(result.stderr)  # curl的详细信息在stderr中
    except Exception as e:
        print(f"❌ 异常: {e}")

    print("\n3. DNS解析测试:")
    # 测试DNS解析
    import socket
    try:
        ip = socket.gethostbyname(domain)
        print(f"✅ DNS解析成功: {domain} -> {ip}")
    except Exception as e:
        print(f"❌ DNS解析失败: {e}")

if __name__ == "__main__":
    # 测试几个域名
    domains = ["d1.api.augmentcode.com", "d7.api.augmentcode.com", "d19.api.augmentcode.com"]
    
    for domain in domains:
        test_single_domain(domain)
        print("\n" + "="*80 + "\n") 