#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
YAugment 网络优化器
用于测速并优化 augmentcode.com 的连接速度
"""

import os
import sys
import time
import socket
import platform
import subprocess
import re
from typing import List, Tuple, Optional
from concurrent.futures import ThreadPoolExecutor, as_completed
import ctypes


class YAugmentOptimizer:
    def __init__(self):
        self.system = platform.system().lower()
        self.hosts_file = self._get_hosts_file_path()
        self.domains = [f"d{i}.api.augmentcode.com" for i in range(1, 21)]
        self.optimization_marker = "# ==== YAugment 优化策略 ===="
        self.optimization_end_marker = "# ==== 不需要可以直接删掉 ===="
        # 代理配置
        self.proxy_config = None
        self.proxy_type = None  # None, 'v2rayN', 'custom'
    
    def _get_hosts_file_path(self) -> str:
        """获取hosts文件路径"""
        if self.system == "windows":
            return r"C:\Windows\System32\drivers\etc\hosts"
        else:  # Linux, macOS
            return "/etc/hosts"
    

    
    def check_admin_privileges(self) -> bool:
        """检查是否有管理员权限"""
        try:
            if self.system == "windows":
                return ctypes.windll.shell32.IsUserAnAdmin()
            else:
                return os.geteuid() == 0
        except Exception:
            return False
    
    def check_curl_available(self) -> bool:
        """检查curl命令是否可用"""
        try:
            result = subprocess.run(['curl', '--version'], capture_output=True, timeout=5)
            return result.returncode == 0
        except Exception:
            return False
    
    def show_proxy_menu(self):
        """显示代理配置菜单"""
        print("\n" + "="*50)
        print("🌐 代理配置")
        print("="*50)
        print("⚠️  注意: Augment 仅支持 HTTP/HTTPS 代理，不支持 SOCKS 代理")
        print("="*50)
        print("请选择代理设置:")
        print("1. 📡 使用 v2rayN 默认配置 (http://127.0.0.1:10808)")
        print("2. 🔥 使用 Clash Verge 默认配置 (http://127.0.0.1:7897)")
        print("3. 🔧 自定义代理地址")
        print("4. 🚫 不使用代理")
        print("="*50)
        
        while True:
            try:
                choice = input("请输入选项 (1-4): ").strip()
                if choice == '1':
                    self.proxy_config = "http://127.0.0.1:10808"
                    self.proxy_type = 'v2rayN'
                    print(f"✅ 已设置 v2rayN 代理: {self.proxy_config}")
                    return True
                elif choice == '2':
                    self.proxy_config = "http://127.0.0.1:7897"
                    self.proxy_type = 'clash-verge'
                    print(f"✅ 已设置 Clash Verge 代理: {self.proxy_config}")
                    return True
                elif choice == '3':
                    proxy_url = input("请输入代理地址 (格式: http://127.0.0.1:8080 或 https://127.0.0.1:8080): ").strip()
                    if self.validate_proxy_url(proxy_url):
                        self.proxy_config = proxy_url
                        self.proxy_type = 'custom'
                        print(f"✅ 已设置自定义代理: {self.proxy_config}")
                        return True
                    else:
                        print("❌ 代理地址格式不正确，请重新输入")
                        print("💡 提示: 只支持 HTTP/HTTPS 代理，格式如 http://127.0.0.1:8080")
                elif choice == '4':
                    self.proxy_config = None
                    self.proxy_type = None
                    print("✅ 已设置不使用代理")
                    return True
                else:
                    print("❌ 无效选项，请输入 1、2、3 或 4")
            except KeyboardInterrupt:
                print("\n用户取消操作")
                return False
            except Exception:
                print("❌ 输入错误，请重新输入")
    
    def validate_proxy_url(self, proxy_url: str) -> bool:
        """验证代理URL格式 - 仅支持 HTTP/HTTPS"""
        if not proxy_url:
            return False
        
        # 仅支持 HTTP/HTTPS 代理格式
        patterns = [
            r'^http://[\w\.-]+:\d+$',
            r'^https://[\w\.-]+:\d+$'
        ]
        
        return any(re.match(pattern, proxy_url) for pattern in patterns)
    
    def test_proxy_connection(self) -> bool:
        """测试代理连接是否有效"""
        if not self.proxy_config:
            return True
        
        print(f"\n🔍 测试代理连接: {self.proxy_config}")
        
        try:
            # 使用curl测试代理连接
            null_device = 'NUL' if self.system == 'windows' else '/dev/null'
            test_url = "https://www.google.com"
            
            curl_cmd = [
                'curl', test_url,
                '--proxy', self.proxy_config,
                '--max-time', '10',
                '--connect-timeout', '5',
                '--silent',
                '--show-error',
                '--output', null_device,
                '--write-out', '%{http_code}'
            ]
            
            result = subprocess.run(curl_cmd, capture_output=True, text=True, timeout=15, encoding='utf-8', errors='ignore')
            
            if result.returncode == 0:
                http_code = result.stdout.strip()
                if http_code in ['200', '301', '302', '403', '404']:  # 这些状态码表示代理连接正常
                    print(f"✅ 代理连接测试成功 (HTTP {http_code})")
                    return True
                else:
                    print(f"⚠️  代理连接异常 (HTTP {http_code})")
                    return False
            else:
                print(f"❌ 代理连接失败: {result.stderr.strip()}")
                return False
                
        except subprocess.TimeoutExpired:
            print("❌ 代理连接测试超时")
            return False
        except Exception as e:
            print(f"❌ 代理连接测试异常: {str(e)}")
            return False
    
    def remove_existing_optimization(self):
        """删除hosts文件中现有的优化策略"""
        try:
            with open(self.hosts_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
        except Exception as e:
            print(f"读取hosts文件失败: {e}")
            return
        
        # 查找优化策略标记
        start_idx = -1
        end_idx = -1
        
        for i, line in enumerate(lines):
            if self.optimization_marker in line:
                start_idx = i
            elif self.optimization_end_marker in line and start_idx != -1:
                end_idx = i
                break
        
        if start_idx != -1 and end_idx != -1:
            # 检查优化策略前面是否有空行，如果有的话也删除
            actual_start = start_idx
            if start_idx > 0 and lines[start_idx - 1].strip() == "":
                actual_start = start_idx - 1
            
            # 删除优化策略部分（包括前面的空行）
            new_lines = lines[:actual_start] + lines[end_idx + 1:]
            try:
                with open(self.hosts_file, 'w', encoding='utf-8') as f:
                    f.writelines(new_lines)
                print("已清除现有的优化策略")
            except Exception as e:
                print(f"清除优化策略失败: {e}")
        else:
            print("未发现现有的优化策略")
    

    
    def test_domain_speed_single(self, domain: str, timeout: float = 8.0) -> Tuple[Optional[float], Optional[str]]:
        """单次测试域名连接速度，返回延迟和真实连接的IP"""
        try:
            start_time = time.time()
            
            # 构建curl命令进行测速
            null_device = 'NUL' if self.system == 'windows' else '/dev/null'
            curl_cmd = [
                'curl', f'https://{domain}/',
                '-H', 'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                '--max-time', str(int(timeout)),
                '--connect-timeout', '5',  # 连接超时
                '--insecure',  # 忽略SSL证书验证
                '--location',  # 跟随重定向
                '--silent',  # 静默模式
                '--show-error',  # 显示错误信息
                '--output', null_device,  # 丢弃响应体
                '--write-out', '%{remote_ip}'  # 输出连接的远程IP
            ]
            
            # 如果配置了代理，添加代理参数
            if self.proxy_config:
                curl_cmd.extend(['--proxy', self.proxy_config])
            
            result = subprocess.run(curl_cmd, capture_output=True, text=True, timeout=timeout + 2, encoding='utf-8', errors='ignore')
            end_time = time.time()
            
            # 检查curl是否成功执行
            if result.returncode == 0:
                latency = (end_time - start_time) * 1000  # 转换为毫秒
                
                # 获取真实的服务器IP
                real_ip = None
                if self.proxy_config:
                    # 使用代理时，通过DNS解析获取真实IP（不使用代理）
                    real_ip = self.get_domain_ip(domain)
                else:
                    # 直连时，使用curl返回的IP
                    curl_ip = result.stdout.strip()
                    ip_pattern = r'^(\d{1,3}\.){3}\d{1,3}$'
                    if curl_ip and re.match(ip_pattern, curl_ip):
                        parts = curl_ip.split('.')
                        if all(0 <= int(part) <= 255 for part in parts):
                            real_ip = curl_ip
                    
                    # 如果curl没有返回有效IP，使用DNS解析
                    if not real_ip:
                        real_ip = self.get_domain_ip(domain)
                
                if real_ip:
                    return latency, real_ip
                else:
                    return latency, None
            
            return None, None
                
        except subprocess.TimeoutExpired:
            print("超时")
            return None, None
        except Exception as e:
            print(f"异常: {str(e)[:50]}")
            return None, None
    
    def test_domain_speed(self, domain: str, test_count: int = 3, timeout: float = 8.0) -> Tuple[Optional[float], Optional[str]]:
        """测试域名连接速度，进行多次测试并取平均值"""
        latencies = []
        best_ip = None
        
        for i in range(test_count):
            latency, ip = self.test_domain_speed_single(domain, timeout)
            if latency is not None:
                latencies.append(latency)
                if best_ip is None and ip is not None:
                    best_ip = ip
        
        if latencies:
            avg_latency = sum(latencies) / len(latencies)
            min_latency = min(latencies)
            max_latency = max(latencies)
            proxy_info = f" [代理: {self.proxy_type}]" if self.proxy_config else ""
            print(f"✅ {domain}: 平均 {avg_latency:.1f}ms (最快 {min_latency:.1f}ms, 最慢 {max_latency:.1f}ms) [{len(latencies)}/{test_count}]{proxy_info}")
            return avg_latency, best_ip
        else:
            print(f"❌ {domain}: 全部失败")
            return None, None
    
    def get_domain_ip(self, domain: str) -> Optional[str]:
        """获取域名的IP地址"""
        try:
            ip = socket.gethostbyname(domain)
            return ip
        except Exception:
            return None
    
    def test_all_domains(self) -> Tuple[str, float, str]:
        """并发测试所有域名，返回最快的域名、延迟和真实IP"""
        proxy_info = f" (使用代理: {self.proxy_config})" if self.proxy_config else " (直连)"
        print(f"🚀 开始并发测速{proxy_info}...")
        print(f"📊 将同时测试 {len(self.domains)} 个域名，每个域名测试3次取平均值")
        print("=" * 60)
        
        results = []
        completed_count = 0
        
        # 使用线程池并发测试
        with ThreadPoolExecutor(max_workers=min(len(self.domains), 10)) as executor:
            # 提交所有测试任务
            future_to_domain = {
                executor.submit(self.test_domain_speed, domain): domain 
                for domain in self.domains
            }
            
            # 收集结果
            for future in as_completed(future_to_domain):
                domain = future_to_domain[future]
                completed_count += 1
                
                try:
                    latency, real_ip = future.result()
                    
                    if latency is not None and real_ip is not None:
                        results.append((domain, latency, real_ip))
                    elif latency is not None:
                        # 如果有延迟但没有获取到真实IP，尝试DNS解析作为备选
                        fallback_ip = self.get_domain_ip(domain)
                        if fallback_ip:
                            results.append((domain, latency, fallback_ip))
                            print(f"⚠️  {domain}: 使用DNS解析IP {fallback_ip}")
                        else:
                            print(f"⚠️  {domain}: 无法获取IP")
                    
                    # 显示进度
                    print(f"📈 进度: {completed_count}/{len(self.domains)} 已完成")
                        
                except Exception as e:
                    print(f"❌ {domain}: 测试异常 - {str(e)[:50]}")
                    print(f"📈 进度: {completed_count}/{len(self.domains)} 已完成")
        
        print("=" * 60)
        
        if not results:
            print("❌ 所有域名测试失败!")
            sys.exit(1)
        
        # 找到延迟最小的域名
        best_domain, best_latency, best_ip = min(results, key=lambda x: x[1])
        print(f"\n🏆 最快的域名: {best_domain} (平均延迟: {best_latency:.1f}ms, 真实IP: {best_ip})")
        
        # 显示前3名
        sorted_results = sorted(results, key=lambda x: x[1])
        print(f"\n📊 测速排名 (成功测试 {len(results)} 个域名):")
        for i, (domain, latency, ip) in enumerate(sorted_results[:min(5, len(sorted_results))], 1):
            print(f"  {i}. {domain}: {latency:.1f}ms ({ip})")
        
        return best_domain, best_latency, best_ip
    
    def update_hosts_file(self, best_domain: str, best_latency: float, best_ip: str):
        """更新hosts文件"""
        # 清理IP地址，确保只包含IP地址本身
        clean_ip = best_ip.strip()
        # 如果包含换行符或其他字符，只取第一行
        if '\n' in clean_ip:
            clean_ip = clean_ip.split('\n')[0].strip()
        
        # 网络环境信息
        network_env = "代理环境" if self.proxy_config else "直连环境"
        proxy_info = f" (通过代理: {self.proxy_config})" if self.proxy_config else ""
        
        optimization_content = [
            f"\n{self.optimization_marker}\n",
            f"# 优化环境: {network_env}\n",
            f"# 最快域名: {best_domain} (平均延迟: {best_latency:.3f}ms{proxy_info})\n",
            f"# 警告: 此优化针对{network_env}，切换网络环境后可能需要重新优化\n"
        ]
        
        # 添加所有域名映射
        for domain in self.domains:
            optimization_content.append(f"{clean_ip}  {domain}\n")
        
        optimization_content.append(f"{self.optimization_end_marker}\n")
        
        try:
            with open(self.hosts_file, 'a', encoding='utf-8') as f:
                f.writelines(optimization_content)
            print(f"已将所有域名指向 {clean_ip}")
            print(f"⚠️  注意: 此优化针对{network_env}")
            if self.proxy_config:
                print(f"⚠️  如果关闭代理直连，可能需要重新优化以获得最佳性能")
            print("hosts文件更新完成!")
        except Exception as e:
            print(f"更新hosts文件失败: {e}")
            sys.exit(1)
    
    def flush_dns(self):
        """清除DNS缓存"""
        try:
            if self.system == "windows":
                subprocess.run(["ipconfig", "/flushdns"], check=True, capture_output=True)
            elif self.system == "darwin":  # macOS
                subprocess.run(["sudo", "dscacheutil", "-flushcache"], check=True, capture_output=True)
            else:  # Linux
                # 尝试多种DNS清除方法
                try:
                    subprocess.run(["sudo", "systemctl", "restart", "systemd-resolved"], check=True, capture_output=True)
                except:
                    try:
                        subprocess.run(["sudo", "service", "networking", "restart"], check=True, capture_output=True)
                    except:
                        pass
            print("DNS缓存已清除")
        except Exception as e:
            print(f"清除DNS缓存失败: {e}")
    
    def show_menu(self):
        """显示功能菜单"""
        print("\n" + "="*50)
        print("🚀 YAugment 网络优化器")
        print("="*50)
        print("请选择要执行的操作:")
        print("1. 🔧 优化API连接速度")
        print("2. 🗑️  删除优化策略")
        print("3. 🚪 退出程序")
        print("="*50)
        
        while True:
            try:
                choice = input("请输入选项 (1-3): ").strip()
                if choice in ['1', '2', '3']:
                    return choice
                else:
                    print("❌ 无效选项，请输入 1、2 或 3")
            except KeyboardInterrupt:
                print("\n用户取消操作")
                sys.exit(0)
            except Exception:
                print("❌ 输入错误，请重新输入")
    
    def run_optimization(self):
        """运行优化流程"""
        print("\n🔧 开始优化API连接速度...")
        
        # 1. 检查curl命令可用性
        if not self.check_curl_available():
            print("❌ 错误: curl命令不可用")
            print("请确保系统已安装curl命令")
            return False
        
        print("✅ curl命令检查通过")
        
        # 2. 配置代理
        if not self.show_proxy_menu():
            return False
        
        # 3. 测试代理连接
        if not self.test_proxy_connection():
            retry = input("\n代理连接失败，是否继续？(y/n): ").strip().lower()
            if retry not in ['y', 'yes']:
                return False
        
        # 4. 清除现有优化策略
        self.remove_existing_optimization()
        
        # 5. 测速所有域名
        best_domain, best_latency, best_ip = self.test_all_domains()
        
        # 6. 更新hosts文件
        self.update_hosts_file(best_domain, best_latency, best_ip)
        
        # 7. 清除DNS缓存
        self.flush_dns()
        
        print("\n🎉 优化完成!")
        print(f"所有 d1-d20.api.augmentcode.com 现在都指向最快的服务器")
        print(f"真实IP: {best_ip}")
        print(f"预期延迟: {best_latency:.2f}ms")
        if self.proxy_config:
            print(f"使用代理: {self.proxy_config}")
        return True
    
    def run_remove_optimization(self):
        """删除优化策略"""
        print("\n🗑️  开始删除优化策略...")
        
        # 检查是否存在优化策略
        try:
            with open(self.hosts_file, 'r', encoding='utf-8') as f:
                content = f.read()
                
            if self.optimization_marker not in content:
                print("ℹ️  未发现优化策略，无需删除")
                return True
                
        except Exception as e:
            print(f"❌ 读取hosts文件失败: {e}")
            return False
        
        # 删除优化策略
        self.remove_existing_optimization()
        
        # 清除DNS缓存
        self.flush_dns()
        
        print("✅ 优化策略已删除")
        print("🔄 DNS缓存已清除")
        return True
    
    def run(self):
        """运行优化器"""
        print("YAugment 网络优化器启动...")
        print(f"操作系统: {platform.system()}")
        print(f"hosts文件路径: {self.hosts_file}")
        
        # 检查管理员权限
        if not self.check_admin_privileges():
            print("❌ 错误: 需要管理员权限才能运行此脚本")
            if self.system == "windows":
                print("请以管理员身份运行命令提示符或PowerShell")
            else:
                print("请使用 sudo 运行此脚本")
            sys.exit(1)
        
        print("✅ 管理员权限检查通过")
        
        # 显示菜单并处理用户选择
        while True:
            choice = self.show_menu()
            
            if choice == '1':
                success = self.run_optimization()
                if success:
                    break
            elif choice == '2':
                success = self.run_remove_optimization()
                if success:
                    break
            elif choice == '3':
                print("👋 感谢使用 YAugment 网络优化器!")
                sys.exit(0)
            
            # 如果操作失败，询问是否继续
            print("\n" + "-"*50)
            retry = input("操作完成，是否返回主菜单? (y/n): ").strip().lower()
            if retry not in ['y', 'yes', '']:
                print("👋 感谢使用 YAugment 网络优化器!")
                break


def main():
    """主函数"""
    try:
        optimizer = YAugmentOptimizer()
        optimizer.run()
    except KeyboardInterrupt:
        print("\n用户取消操作")
        sys.exit(0)
    except Exception as e:
        print(f"发生错误: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main() 