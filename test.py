import os
import sys
import json
import platform
import sqlite3
import requests
import asyncio
import aiohttp
import threading
import time
import subprocess
import tempfile
import traceback
import re
import urllib.request
from collections import deque
from datetime import datetime, timedelta, timezone
from dateutil import parser, tz
from PyQt6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QPushButton, QLabel, 
    QVBoxLayout, QHBoxLayout, QStackedWidget, QFrame, QProgressBar, QLineEdit, QMessageBox,
    QSizePolicy, QSpacerItem, QGraphicsOpacityEffect, QGraphicsDropShadowEffect,
    QDialog, QComboBox, QTabWidget, QGraphicsBlurEffect, QGridLayout, QToolButton,
    QCheckBox, QGroupBox, QRadioButton, QFormLayout, QTextEdit, QSpinBox, QDateEdit,
    QFileDialog, QStyle, QScrollArea
)
from PyQt6.QtCore import (
    Qt, QPropertyAnimation, QEasingCurve, QParallelAnimationGroup, 
    QSequentialAnimationGroup, QPoint, QSize, QTimer, pyqtSignal, QObject, QThread, QRect, 
    QAbstractAnimation, QDateTime, QDate, pyqtProperty, QEvent
)
from PyQt6.QtGui import (
    QColor, QPalette, QFont, QIcon, QMovie, QPixmap, QPainter, QFontMetrics, QLinearGradient,
    QPen, QPainterPath, QCursor, QBrush, QTransform
)


# 颜色主题
class Theme:
    # 主色
    PRIMARY = "#121317"  # 主背景色（深黑色）
    SECONDARY = "#1A1D23"  # 次要背景色（稍浅的黑色）
    ACCENT = "#2B9D7C"  # 暗绿色强调色（更暗的绿色）
    ACCENT_HOVER = "#34B892"  # 悬停时的强调色
    TEXT_PRIMARY = "#FFFFFF"  # 主要文本颜色
    TEXT_SECONDARY = "#9CA2AE"  # 次要文本颜色
    
    # 状态颜色
    SUCCESS = "#2B9D7C"  # 成功状态（暗绿色）
    WARNING = "#CBAF67"  # 警告状态（暗黄色）
    ERROR = "#BC4A59"  # 错误状态（暗红色）
    GOLD = "#FFD700"    # 金色（用于高级用户显示）
    
    # 其他颜色
    CARD_BG = "#1E2128"  # 卡片背景
    BORDER = "#2A2E36"  # 边框颜色
    HOVER = "#252830"  # 悬停颜色
    SELECTION = "#2B9D7C"  # 选中颜色
    DISABLED = "#3A3E47"  # 禁用状态颜色
    
    # 卡片层级
    CARD_LEVEL_1 = "#1A1D23"  # 一级卡片
    CARD_LEVEL_2 = "#21252D"  # 二级卡片
    CARD_LEVEL_3 = "#282C36"  # 三级卡片
    
    # 窗口控制按钮
    WINDOW_CLOSE = "#BC4A59"
    WINDOW_CLOSE_HOVER = "#D04A57"
    WINDOW_MINIMIZE = "#CBAF67"
    WINDOW_MINIMIZE_HOVER = "#E8D28D"
    WINDOW_MAXIMIZE = "#2B9D7C"
    WINDOW_MAXIMIZE_HOVER = "#34B892"
    
    # 毛玻璃效果
    GLASS_BG = "rgba(30, 33, 40, 0.75)"  # 半透明背景
    GLASS_BORDER = "rgba(60, 63, 70, 0.35)"  # 半透明边框
    GLASS_SHADOW = "rgba(0, 0, 0, 0.2)"  # 阴影颜色
    
    # 渐变
    GRADIENT_START = "#121317"
    GRADIENT_END = "#1A1D23"
    
    # 字体
    FONT_FAMILY = "'Segoe UI', 'Microsoft YaHei UI', 'SF Pro Display', 'Helvetica Neue', Arial, sans-serif"
    FONT_SIZE_SMALL = "12px"
    FONT_SIZE_NORMAL = "14px"
    FONT_SIZE_TITLE = "18px"
    FONT_SIZE_HEADER = "24px"
    
    # 圆角
    BORDER_RADIUS = "14px"
    BORDER_RADIUS_SMALL = "10px"
    
    # 间距
    SPACING_SMALL = "8px"
    SPACING_MEDIUM = "16px"
    SPACING_LARGE = "24px"
    
    # 动画时间 - 用于QPropertyAnimation
    ANIMATION_FAST = 150  # ms
    ANIMATION_NORMAL = 250  # ms 
    ANIMATION_SLOW = 350  # ms
    
    # 阴影
    SHADOW_SMALL = "0 4px 8px rgba(0, 0, 0, 0.15)"
    SHADOW_MEDIUM = "0 6px 12px rgba(0, 0, 0, 0.2)"
    SHADOW_LARGE = "0 8px 16px rgba(0, 0, 0, 0.25)"
    
    # 卡片悬停提升
    CARD_LIFT = "transform: translateY(-2px);"
    
    # 交互动画
    # 已移除不支持的CSS属性
    # TRANSITION_NORMAL 和 TRANSITION_FAST


# AnimatedStackedWidget类定义在Theme类前
class AnimatedStackedWidget(QStackedWidget):
    """带有动画效果的堆叠窗口部件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.m_direction = Qt.Orientation.Horizontal
        self.m_speed = 300
        self.m_animationtype = QEasingCurve.Type.OutCubic
        self.m_current = 0
        self.m_next = 0
        self.m_wrap = False
        self.m_pnow = QPoint(0, 0)
        self.m_active = False
        
    def setDirection(self, direction):
        """设置动画方向：Qt.Orientation.Horizontal（水平）或Qt.Orientation.Vertical（垂直）"""
        self.m_direction = direction
        
    def setSpeed(self, speed):
        """设置动画速度"""
        self.m_speed = speed
        
    def setAnimation(self, animation_type):
        """设置动画曲线类型"""
        self.m_animationtype = animation_type
        
    def setWrap(self, wrap):
        """设置是否循环切换"""
        self.m_wrap = wrap
        
    def slideInNext(self):
        """滑动到下一个窗口"""
        now = self.currentIndex()
        if self.m_wrap or now < self.count() - 1:
            self.slideInIdx(now + 1)
            
    def slideInPrev(self):
        """滑动到前一个窗口"""
        now = self.currentIndex()
        if self.m_wrap or now > 0:
            self.slideInIdx(now - 1)
            
    def slideInIdx(self, idx, direction=None):
        """滑动到指定索引窗口"""
        if idx > self.count() - 1:
            if self.m_wrap:
                idx = 0
            else:
                idx = self.count() - 1
        elif idx < 0:
            if self.m_wrap:
                idx = self.count() - 1
            else:
                idx = 0
                
        if self.m_active:
            return
            
        self.m_active = True
        
        # 记录当前索引和下一个索引
        self.m_current = self.currentIndex()
        self.m_next = idx
        
        # 如果当前与目标相同则返回
        if self.m_current == self.m_next:
            self.m_active = False
            return
            
        # 根据切换方向设置偏移
        if direction is not None:
            directionhint = direction
        elif self.m_current < self.m_next:
            directionhint = self.m_direction
        else:
            directionhint = Qt.Orientation.Horizontal if self.m_direction == Qt.Orientation.Vertical else Qt.Orientation.Vertical
            
        # 获取当前窗口的矩形区域
        current_widget = self.widget(self.m_current)
        next_widget = self.widget(self.m_next)
        self.m_pnow = self.pos()
        
        # 设置下一个窗口的初始位置
        if directionhint == Qt.Orientation.Horizontal:
            if self.m_current < self.m_next:
                # 向左滚动
                next_widget.setGeometry(self.width(), 0, self.width(), self.height())
                end_x = 0
                end_y = 0
                start_x = -self.width()
                start_y = 0
            else:
                # 向右滚动
                next_widget.setGeometry(-self.width(), 0, self.width(), self.height())
                end_x = 0
                end_y = 0
                start_x = self.width()
                start_y = 0
        else:
            if self.m_current < self.m_next:
                # 向上滚动
                next_widget.setGeometry(0, self.height(), self.width(), self.height())
                end_x = 0
                end_y = 0
                start_x = 0
                start_y = -self.height()
            else:
                # 向下滚动
                next_widget.setGeometry(0, -self.height(), self.width(), self.height())
                end_x = 0
                end_y = 0
                start_x = 0
                start_y = self.height()
        
        # 显示下一个窗口
        next_widget.show()
        next_widget.raise_()
        
        # 创建动画组
        self.anim_group = QParallelAnimationGroup(self)
        
        # 当前窗口的滑出动画
        animation_current = QPropertyAnimation(current_widget, b"pos")
        animation_current.setDuration(self.m_speed)
        animation_current.setEasingCurve(self.m_animationtype)
        animation_current.setStartValue(QPoint(0, 0))
        animation_current.setEndValue(QPoint(start_x, start_y))
        
        # 下一个窗口的滑入动画
        animation_next = QPropertyAnimation(next_widget, b"pos")
        animation_next.setDuration(self.m_speed)
        animation_next.setEasingCurve(self.m_animationtype)
        animation_next.setStartValue(QPoint(-start_x, -start_y))
        animation_next.setEndValue(QPoint(end_x, end_y))
        
        # 添加动画到动画组
        self.anim_group.addAnimation(animation_current)
        self.anim_group.addAnimation(animation_next)
        
        # 连接动画结束信号
        self.anim_group.finished.connect(self.animationDoneSlot)
        
        # 启动动画
        self.anim_group.start()
    
    def animationDoneSlot(self):
        """动画完成后的槽函数"""
        # 设置当前窗口索引
        self.setCurrentIndex(self.m_next)
        
        # 重置所有窗口的位置
        for i in range(self.count()):
            widget = self.widget(i)
            widget.setGeometry(0, 0, self.width(), self.height())
            
        # 重置动画状态
        self.m_active = False
        
        # 重新设置窗口位置
        self.move(self.m_pnow)
        
        # 发射动画完成信号
        self.animationFinished.emit()
    
    # 定义动画完成信号
    animationFinished = pyqtSignal()


# 添加一个自定义的无下划线菜单按钮类
class NoUnderlineMenuButton(QPushButton):
    """没有下划线的菜单按钮"""
    
    def __init__(self, text, parent=None):
        super().__init__(text, parent)
        self.setStyleSheet("""
            QPushButton {
                padding: 12px 20px;
                text-align: left;
                font-size: 16px;
                border: none;
                border-bottom: none;
                background-color: transparent;
                color: #9CA2AE;
                font-weight: normal;
                border-radius: 10px;
                text-decoration: none;
            }
            QPushButton:hover {
                background-color: #1A1D23;
                color: #FFFFFF;
            }
            QPushButton:checked {
                background-color: #2B9D7C;
                color: #FFFFFF;
                font-weight: bold;
                border-bottom: none;
            }
        """)
        
    def paintEvent(self, event):
        # 自定义绘制，确保不会出现下划线
        super().paintEvent(event)
        
    def focusInEvent(self, event):
        # 阻止显示焦点框
        super().focusInEvent(event)
        self.clearFocus()


# Cursor认证信息管理器
class CursorAuthManager:
    """Cursor认证信息管理器"""

    def __init__(self):
        # 判断操作系统
        if sys.platform == "win32":  # Windows
            appdata = os.getenv("APPDATA")
            if appdata is None:
                raise EnvironmentError("APPDATA 环境变量未设置")
            self.db_path = os.path.join(
                appdata, "Cursor", "User", "globalStorage", "state.vscdb"
            )
        elif sys.platform == "darwin": # macOS
            self.db_path = os.path.abspath(os.path.expanduser(
                "~/Library/Application Support/Cursor/User/globalStorage/state.vscdb"
            ))
        elif sys.platform == "linux" : # Linux 和其他类Unix系统
            self.db_path = os.path.abspath(os.path.expanduser(
                "~/.config/Cursor/User/globalStorage/state.vscdb"
            ))
        else:
            raise NotImplementedError(f"不支持的操作系统: {sys.platform}")

    def get_current_email(self):
        """
        获取当前登录的邮箱地址
        :return: str 当前登录的邮箱地址，如果未找到则返回None
        """
        conn = None
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 查询当前登录的邮箱地址
            cursor.execute("SELECT value FROM itemTable WHERE key = 'cursorAuth/cachedEmail'")
            result = cursor.fetchone()
            
            if result is not None:
                return result[0]
            else:
                return None
        except sqlite3.Error as e:
            print(f"数据库错误: {str(e)}")
            return None
        except Exception as e:
            print(f"发生错误: {str(e)}")
            return None
        finally:
            if conn:
                conn.close()

    def update_auth(self, email=None, access_token=None, refresh_token=None):
        """
        更新Cursor的认证信息
        :param email: 新的邮箱地址
        :param access_token: 新的访问令牌
        :param refresh_token: 新的刷新令牌
        :return: bool 是否成功更新
        """
        updates = []
        # 登录状态
        updates.append(("cursorAuth/cachedSignUpType", "Auth_0"))

        if email is not None:
            updates.append(("cursorAuth/cachedEmail", email))
        if access_token is not None:
            updates.append(("cursorAuth/accessToken", access_token))
        if refresh_token is not None:
            updates.append(("cursorAuth/refreshToken", refresh_token))

        if not updates:
            print("没有提供任何要更新的值")
            return False

        conn = None
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            for key, value in updates:
                # 检查键是否存在
                check_query = "SELECT COUNT(*) FROM itemTable WHERE key = ?"
                cursor.execute(check_query, (key,))
                if cursor.fetchone()[0] == 0:
                    insert_query = "INSERT INTO itemTable (key, value) VALUES (?, ?)"
                    cursor.execute(insert_query, (key, value))
                else:
                    update_query = "UPDATE itemTable SET value = ? WHERE key = ?"
                    cursor.execute(update_query, (value, key))

            conn.commit()
            return True

        except sqlite3.Error as e:
            print(f"数据库错误: {str(e)}")
            return False
        except Exception as e:
            print(f"发生错误: {str(e)}")
            return False
        finally:
            if conn:
                conn.close()


# 数据处理相关的类

# 定义额度相关字段常量
ACCOUNT_QUOTA_FIELDS = ["real_usage", "real_max_usage", "real_register_time", "real_remaining_days", "quota_data", "api_register_time"]

class AccountData:
    """账户数据管理类"""
    
    def __init__(self, accounts_file="cursor_accounts.json"):
        # 获取应用程序路径 - 兼容PyInstaller打包和普通Python运行
        if getattr(sys, 'frozen', False):
            # 如果是使用PyInstaller打包后的应用
            application_path = os.path.dirname(sys.executable)
        else:
            # 如果是普通Python脚本运行
            application_path = os.path.dirname(os.path.abspath(__file__))
            
        # 配置文件路径
        self.accounts_file = os.path.join(application_path, accounts_file)
        print(f"正在使用配置文件: {self.accounts_file}")
        
        # 创建临时数据文件路径 - 仅用于存储额度数据
        self.temp_file = os.path.join(application_path, "temp_quota_" + accounts_file)
        
        # 检查同名临时文件是否存在，如果存在则先删除
        if os.path.exists(self.temp_file):
            try:
                os.remove(self.temp_file)
                print(f"删除了已存在的临时文件: {self.temp_file}")
            except Exception as e:
                print(f"删除已存在临时文件失败: {str(e)}")
        
        # 加载账户数据
        self.accounts = self.load_accounts()
        
        # 从临时文件加载配额数据（如果有）
        self.load_quotas_from_temp()
    
    def _clean_account_data(self, account):
        """创建不包含额度字段的账户数据副本"""
        clean_account = account.copy()
        for field in ACCOUNT_QUOTA_FIELDS:
            clean_account.pop(field, None)
        return clean_account
    
    def load_accounts(self):
        """加载账户信息"""
        try:
            if not os.path.exists(self.accounts_file):
                print(f"账号文件不存在: {self.accounts_file}")
                return []
                
            with open(self.accounts_file, 'r', encoding='utf-8') as f:
                accounts = json.load(f)
                
            if not accounts:
                print("账号文件为空")
                return []
            
            # 反转账户列表顺序，使最新添加的账户（文件末尾的账户）排在前面
            accounts.reverse()    
            return accounts
        except Exception as e:
            print(f"读取账号文件失败: {str(e)}")
            return []
    
    def save_quotas_to_temp(self):
        """保存账户额度数据到临时文件"""
        try:
            # 提取账户中的配额相关数据
            quota_data = []
            for account in self.accounts:
                # 只保存账户邮箱和额度相关数据
                account_quota = {
                    "email": account.get("email", ""),
                    "real_usage": account.get("real_usage", 0),
                    "real_max_usage": account.get("real_max_usage", 0),
                    "real_register_time": account.get("real_register_time", ""),
                    "real_remaining_days": account.get("real_remaining_days", ""),
                    "quota_data": account.get("quota_data", {}),
                    "api_register_time": account.get("api_register_time", "")
                }
                quota_data.append(account_quota)
            
            # 保存到临时文件
            with open(self.temp_file, 'w', encoding='utf-8') as f:
                json.dump(quota_data, f, ensure_ascii=False, indent=2)
            print(f"额度数据已保存到临时文件: {self.temp_file}")
            return True
        except Exception as e:
            print(f"保存额度临时文件失败: {str(e)}")
            return False
    
    def load_quotas_from_temp(self):
        """从临时文件加载额度数据"""
        try:
            if not os.path.exists(self.temp_file):
                print(f"临时额度文件不存在: {self.temp_file}")
                return False
            
            with open(self.temp_file, 'r', encoding='utf-8') as f:
                temp_data = json.load(f)
            
            if not temp_data:
                print("临时额度文件为空")
                return False
            
            # 将临时文件中的额度数据合并到账户数据中
            for account in self.accounts:
                email = account.get("email", "")
                # 查找对应的额度数据
                for quota_item in temp_data:
                    if quota_item.get("email") == email:
                        # 更新额度相关字段
                        account["real_usage"] = quota_item.get("real_usage", 0)
                        account["real_max_usage"] = quota_item.get("real_max_usage", 0)
                        account["real_register_time"] = quota_item.get("real_register_time", "")
                        account["real_remaining_days"] = quota_item.get("real_remaining_days", "")
                        account["quota_data"] = quota_item.get("quota_data", {})
                        account["api_register_time"] = quota_item.get("api_register_time", "")
                        break
            
            print(f"已从临时文件加载额度数据")
            return True
        except Exception as e:
            print(f"从临时文件加载额度数据失败: {str(e)}")
            return False
    
    def save_accounts_main_only(self):
        """只保存账户基本信息到主文件，不涉及临时文件"""
        try:
            # 仅在文件已存在时保存，避免自动创建文件
            if not os.path.exists(self.accounts_file):
                print(f"账号文件不存在，跳过保存: {self.accounts_file}")
                return False
                
            # 从当前内存中的账户数据移除额度相关字段
            clean_accounts = []
            for account in self.accounts:
                clean_accounts.append(self._clean_account_data(account))
            
            # 反转回原始顺序（最新的放在列表末尾）
            clean_accounts.reverse()
            
            # 只有文件已存在时才保存
            with open(self.accounts_file, 'w', encoding='utf-8') as f:
                json.dump(clean_accounts, f, ensure_ascii=False, indent=2)
            
            return True
        except Exception as e:
            print(f"保存账号文件失败: {str(e)}")
            return False
            
    def save_accounts(self):
        """保存账户信息到主文件和临时文件"""
        # 先保存主文件
        result = self.save_accounts_main_only()
        
        # 然后保存临时文件
        temp_result = self.save_quotas_to_temp()
        
        return result and temp_result
    
    def get_remaining_days(self, register_time):
        """
        计算账户剩余天数
        :param register_time: 注册时间
        :return: 剩余天数或"已过期"
        """
        try:
            # 解析注册时间，支持多种常见格式
            try:
                reg_date = datetime.strptime(register_time, "%Y-%m-%d %H:%M:%S")
            except ValueError:
                try:
                    # 尝试其他可能的日期格式
                    reg_date = datetime.strptime(register_time, "%Y-%m-%d")
                except ValueError:
                    try:
                        # 尝试ISO格式带Z时区的日期格式 (例如："2025-03-21T04:10:22.000Z")
                        from dateutil import parser, tz
                        
                        # 解析日期，会自动处理时区
                        reg_date = parser.parse(register_time)
                        
                        # 如果是时区感知的日期时间，转换为naive时间
                        if reg_date.tzinfo is not None:
                            # 转换为UTC时间
                            reg_date = reg_date.astimezone(tz.UTC)
                            # 移除时区信息，保留UTC时间
                            reg_date = reg_date.replace(tzinfo=None)
                    except (ValueError, ImportError):
                        print(f"无法解析日期格式: {register_time}")
                        return "未知"
            
            # 获取当前时间（无时区信息）
            now = datetime.now()
            
            # 计算总试用期限（14天）
            trial_period = timedelta(days=14)
            expiry_date = reg_date + trial_period
            
            # 计算已经使用的时间
            used_time = now - reg_date
            used_days = used_time.days
            
            # 如果已过期
            if now > expiry_date:
                return "已过期"
            else:
                # 计算剩余天数（总试用期14天 - 已使用天数）
                remaining_days = 14 - used_days
                return remaining_days
        except Exception as e:
            print(f"计算剩余天数时出错: {str(e)}")
            return "未知"
    
    def delete_account(self, email):
        """
        删除指定邮箱的账号
        :param email: 要删除的账号邮箱
        :return: 是否删除成功
        """
        try:
            self.accounts = [acc for acc in self.accounts if acc['email'] != email]
            # 删除账户时需要同时更新主文件和临时文件
            return self.save_accounts()
        except Exception as e:
            print(f"删除账号时出错: {str(e)}")
            return False
    
    def delete_expired_accounts(self):
        """
        删除所有过期账号
        :return: 删除的账号邮箱列表
        """
        try:
            original_accounts = self.accounts.copy()
            
            # 修改判断条件，使用real_remaining_days而不是重新计算
            self.accounts = [
                acc for acc in self.accounts 
                if not (acc.get('real_remaining_days') == "已过期" or acc.get('real_remaining_days') == "试用Pro已过期")
            ]
            
            # 找出被删除的账户邮箱
            original_emails = {acc.get('email') for acc in original_accounts}
            remaining_emails = {acc.get('email') for acc in self.accounts}
            deleted_emails = list(original_emails - remaining_emails)
            
            if deleted_emails:
                # 保存更改到主文件和临时文件
                self.save_accounts()
                
            return deleted_emails
        except Exception as e:
            print(f"删除过期账号时出错: {str(e)}")
            return []
    
    def delete_accounts_by_quota(self, quota, operator="大于等于"):
        """
        删除指定额度的账号
        :param quota: 要删除的账号额度
        :param operator: 操作符，"大于等于"或"小于等于"或"等于"
        :return: 删除的账号数量
        """
        try:
            quota = int(quota)
            original_accounts = self.accounts.copy()
            
            # 保存要删除的账户，而不是直接修改self.accounts
            accounts_to_keep = []
            
            for acc in self.accounts:
                # 获取账户的实际高级模型使用次数(real_usage字段)
                # 这个字段是通过API请求获取的高级模型使用次数，不是usage_limit
                acc_usage = int(acc.get('real_usage', 0))
                
                # 根据条件判断是否要删除
                if operator == "大于等于" and acc_usage >= quota:
                    # 不保留此账户
                    pass
                elif operator == "小于等于" and acc_usage <= quota:
                    # 不保留此账户
                    pass
                elif operator == "等于" and acc_usage == quota:
                    # 不保留此账户
                    pass
                else:
                    accounts_to_keep.append(acc)
            
            # 找出被删除的账户邮箱
            original_emails = {acc.get('email') for acc in original_accounts}
            remaining_emails = {acc.get('email') for acc in accounts_to_keep}
            deleted_emails = list(original_emails - remaining_emails)
            
            # 更新账户列表
            self.accounts = accounts_to_keep
            
            if deleted_emails:
                # 保存更改到主文件和临时文件
                self.save_accounts()
            
            return deleted_emails
        except Exception as e:
            print(f"按额度删除账号时出错: {str(e)}")
            return []

    def update_account_info(self, email, usage=None, max_usage=None, register_time=None, quota_data=None):
        """
        更新账户信息，包括使用量和最大使用量
        :param email: 账户邮箱
        :param usage: 当前使用量
        :param max_usage: 最大使用量
        :param register_time: 注册时间
        :param quota_data: 额度详细数据
        :return: 是否更新成功
        """
        try:
            # 查找账户
            for account in self.accounts:
                if account.get('email') == email:
                    # 更新使用量
                    if usage is not None:
                        account['real_usage'] = usage
                    
                    # 更新最大使用量
                    if max_usage is not None:
                        account['real_max_usage'] = max_usage
                    
                    # 更新注册时间
                    if register_time is not None:
                        account['real_register_time'] = register_time
                        # 计算剩余天数
                        account['real_remaining_days'] = self.get_remaining_days(register_time)
                    
                    # 更新额度详细数据
                    if quota_data is not None:
                        account['quota_data'] = quota_data
                    
                    # 只保存配额数据到临时文件，不修改主文件
                    return self.save_quotas_to_temp()
            
            # 未找到账户
            return False
        except Exception as e:
            print(f"更新账户信息时出错: {str(e)}")
            return False


# 用于获取账户额度的类
class AccountQuota:
    """获取账户额度的类"""
    
    @staticmethod
    def get_quota(account_data):
        """
        获取账户使用额度
        :param account_data: 账户数据，可以是cookies数组或包含auth_info的账户字典
        :return: 账户额度信息
        """
        try:
            # 获取账户的邮箱，用于日志记录
            email = account_data.get('email', 'unknown') if isinstance(account_data, dict) else 'unknown'
            
            # 修改后采用拼接方式构建token值
            fixed_prefix = "user_01000000000000000000000000%3A%3A"
            
            # 获取accessToken的方式1：从auth_info中获取
            access_token = None
            
            # 检查account_data是否是字典类型且包含auth_info
            if isinstance(account_data, dict) and 'auth_info' in account_data:
                auth_info = account_data.get('auth_info', {})
                access_token = auth_info.get('cursorAuth/accessToken')
                print(f"从auth_info中获取到账户 {email} 的accessToken，长度: {len(access_token) if access_token else 0}")
            # 获取accessToken的方式2：从cookies中获取
            elif isinstance(account_data, list):  # 旧方式，传入的是cookies数组
                cookies = account_data
                for cookie in cookies:
                    if cookie.get('name') == 'WorkosCursorSessionToken':
                        # 尝试从cookie中提取access_token部分
                        cookie_value = cookie.get('value', '')
                        if "%3A%3A" in cookie_value:
                            access_token = cookie_value.split("%3A%3A")[-1]
                            print(f"从cookies中提取账户的accessToken，长度: {len(access_token) if access_token else 0}")
                        break
            
            if not access_token:
                print(f"错误: 无法获取账户 {email} 的accessToken")
                return None
                
            # 构建完整的session token
            token_value = fixed_prefix + access_token
            
            # 创建cookies字典
            cookies_dict = {'WorkosCursorSessionToken': token_value}
            
            headers = {
                'authority': 'www.cursor.com',
                'accept': '*/*',
                'accept-language': 'zh-CN,zh;q=0.9',
                'dnt': '1',
                'referer': 'https://www.cursor.com/cn/settings',
                'sec-ch-ua': '"Chromium";v="122", "Not(A:Brand";v="24", "Google Chrome";v="122"',
                'sec-ch-ua-mobile': '?0',
                'sec-ch-ua-platform': '"Windows"',
                'sec-fetch-dest': 'empty',
                'sec-fetch-mode': 'cors',
                'sec-fetch-site': 'same-origin',
                'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.6261.95 Safari/537.36',
            }
            
            print(f"开始获取账户 {email} 的额度信息...")
            response = requests.get('https://www.cursor.com/api/usage', cookies=cookies_dict, headers=headers)
            
            if response.status_code == 200:
                quota_data = response.json()
                print(f"成功获取账户 {email} 的额度信息")
                return quota_data
            else:
                print(f"获取账户 {email} 额度失败: 状态码 {response.status_code}, 响应内容: {response.text}")
                return None
        except Exception as e:
            email = account_data.get('email', 'unknown') if isinstance(account_data, dict) else 'unknown'
            print(f"获取账户 {email} 额度时出错: {str(e)}")
            import traceback
            traceback.print_exc()
            return None


# 异步并发获取账户额度的类
class QuotaFetcher(QObject):
    """异步并发获取账户额度的类"""
    
    # 定义信号
    account_quota_updated = pyqtSignal(str, dict)
    all_quotas_fetched = pyqtSignal()
    
    def __init__(self, accounts):
        super().__init__()
        self.accounts = accounts
        self.max_workers = min(10, os.cpu_count() * 2 + 1)  # 自动设置并发数
        
    def start_fetching(self):
        """开始获取所有账户的额度"""
        thread = threading.Thread(target=self._fetch_all_quotas)
        thread.daemon = True
        thread.start()
    
    def _fetch_all_quotas(self):
        """获取所有账户的额度"""
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        loop.run_until_complete(self._fetch_quotas_async())
        loop.close()
        self.all_quotas_fetched.emit()
        
    async def _fetch_quotas_async(self):
        """异步获取所有账户的额度"""
        tasks = []
        semaphore = asyncio.Semaphore(self.max_workers)
        
        for account in self.accounts:
            task = asyncio.create_task(self._fetch_single_quota(semaphore, account))
            tasks.append(task)
            
        await asyncio.gather(*tasks)
    
    async def _fetch_single_quota(self, semaphore, account):
        """获取单个账户的额度"""
        async with semaphore:
            try:
                # 使用同步函数获取额度
                email = account['email']
                
                # 传递整个账户对象而不只是cookies
                quota_data = await asyncio.to_thread(AccountQuota.get_quota, account)
                
                if quota_data:
                    self.account_quota_updated.emit(email, quota_data)
            except Exception as e:
                print(f"获取账户 {account.get('email', 'unknown')} 的额度时出错: {str(e)}")
                
            # 为避免API限制，适当延迟
            await asyncio.sleep(0.5)


# 实用工具类
class Utils:
    @staticmethod
    def set_dark_theme(app):
        """设置应用程序的暗色主题"""
        dark_palette = QPalette()
        
        # 设置颜色组
        dark_palette.setColor(QPalette.ColorRole.Window, QColor(Theme.PRIMARY))
        dark_palette.setColor(QPalette.ColorRole.WindowText, QColor(Theme.TEXT_PRIMARY))
        dark_palette.setColor(QPalette.ColorRole.Base, QColor(Theme.SECONDARY))
        dark_palette.setColor(QPalette.ColorRole.AlternateBase, QColor(Theme.CARD_BG))
        dark_palette.setColor(QPalette.ColorRole.ToolTipBase, QColor(Theme.TEXT_PRIMARY))
        dark_palette.setColor(QPalette.ColorRole.ToolTipText, QColor(Theme.TEXT_PRIMARY))
        dark_palette.setColor(QPalette.ColorRole.Text, QColor(Theme.TEXT_PRIMARY))
        dark_palette.setColor(QPalette.ColorRole.Button, QColor(Theme.SECONDARY))
        dark_palette.setColor(QPalette.ColorRole.ButtonText, QColor(Theme.TEXT_PRIMARY))
        dark_palette.setColor(QPalette.ColorRole.BrightText, QColor(Theme.ACCENT))
        dark_palette.setColor(QPalette.ColorRole.Link, QColor(Theme.ACCENT))
        dark_palette.setColor(QPalette.ColorRole.Highlight, QColor(Theme.SELECTION))
        dark_palette.setColor(QPalette.ColorRole.HighlightedText, QColor(Theme.TEXT_PRIMARY))
        
        # 应用暗色主题
        app.setPalette(dark_palette)
        
        # 设置应用程序样式表
        app.setStyleSheet(f"""
            QMainWindow, QWidget {{
                background-color: {Theme.PRIMARY};
                color: {Theme.TEXT_PRIMARY};
                font-family: {Theme.FONT_FAMILY};
            }}
            
            /* 全局禁用所有控件的焦点框 */
            * {{
                outline: none;
            }}
            
            QScrollArea {{
                border: none;
                background-color: transparent;
            }}
            
            QScrollBar:vertical {{
                background: {Theme.PRIMARY};
                width: 8px;
                margin: 0px;
            }}
            
            QScrollBar::handle:vertical {{
                background: {Theme.BORDER};
                min-height: 20px;
                border-radius: {Theme.BORDER_RADIUS_SMALL};
            }}
            
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {{
                height: 0px;
            }}
            
            QScrollBar:horizontal {{
                background: {Theme.PRIMARY};
                height: 8px;
                margin: 0px;
            }}
            
            QScrollBar::handle:horizontal {{
                background: {Theme.BORDER};
                min-width: 20px;
                border-radius: {Theme.BORDER_RADIUS_SMALL};
            }}
            
            QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {{
                width: 0px;
            }}
            
            QPushButton {{
                background-color: {Theme.SECONDARY};
                color: {Theme.TEXT_PRIMARY};
                border: 1px solid {Theme.BORDER};
                border-radius: {Theme.BORDER_RADIUS};
                padding: 10px 16px;
                font-weight: bold;
                outline: none; /* 明确禁用按钮的焦点框 */
            }}
            
            QPushButton:focus {{
                outline: none; /* 确保在焦点状态下也不显示焦点框 */
                border: 1px solid {Theme.BORDER}; /* 保持普通边框 */
            }}
            
            QPushButton:hover {{
                background-color: {Theme.HOVER};
                border: 1px solid {Theme.ACCENT};
                color: {Theme.ACCENT};
            }}
            
            QPushButton:pressed {{
                background-color: {Theme.ACCENT};
                color: {Theme.PRIMARY};
            }}
            
            QPushButton:disabled {{
                background-color: {Theme.DISABLED};
                color: {Theme.TEXT_SECONDARY};
                border: 1px solid {Theme.BORDER};
            }}
            
            QLineEdit {{
                background-color: {Theme.SECONDARY};
                color: {Theme.TEXT_PRIMARY};
                border: 1px solid {Theme.BORDER};
                border-radius: {Theme.BORDER_RADIUS};
                padding: 10px;
                selection-background-color: {Theme.ACCENT};
            }}
            
            QLineEdit:focus {{
                border: 1px solid {Theme.ACCENT};
            }}
            
            QLabel {{
                color: {Theme.TEXT_PRIMARY};
            }}
            
            QProgressBar {{
                border: none;
                border-radius: {Theme.BORDER_RADIUS};
                text-align: center;
                background-color: {Theme.SECONDARY};
                font-weight: bold;
                height: 20px;
            }}
            
            QProgressBar::chunk {{
                background-color: {Theme.ACCENT};
                border-radius: {Theme.BORDER_RADIUS_SMALL};
            }}
        """)

    @staticmethod
    def get_cursor_version():
        """获取Cursor版本"""
        try:
            if sys.platform == "win32":  # Windows
                cursor_path = os.path.join(os.getenv("LOCALAPPDATA"), "Programs", "cursor", "Cursor.exe")
                # 使用PowerShell获取文件版本信息
                command = f'powershell -command "(Get-Item \'{cursor_path}\').VersionInfo.ProductVersion"'
                version = subprocess.check_output(command, shell=True).decode().strip()
                return version
            elif sys.platform == "darwin":  # macOS
                cursor_path = "/Applications/Cursor.app/Contents/Info.plist"
                if os.path.exists(cursor_path):
                    command = f'defaults read {cursor_path} CFBundleShortVersionString'
                    version = subprocess.check_output(command, shell=True).decode().strip()
                    return version
                else:
                    return "未找到"
            else:  # Linux
                return "未知"
        except Exception as e:
            print(f"获取Cursor版本时出错: {str(e)}")
            return "未知"

    @staticmethod
    def show_message(parent, title, text, icon=QMessageBox.Icon.Information):
        """显示消息框"""
        return StyledDialog.showInfoDialog(parent, title, text)

    @staticmethod
    def confirm_message(parent, title, text, icon=QMessageBox.Icon.Question):
        """显示确认消息框"""
        # 对于错误图标，使用红色按钮
        if icon == QMessageBox.Icon.Critical:
            confirm_color = Theme.ERROR
        # 对于警告图标，使用黄色按钮
        elif icon == QMessageBox.Icon.Warning:
            confirm_color = Theme.WARNING
        # 其他情况使用默认绿色
        else:
            confirm_color = Theme.ACCENT
            
        return StyledDialog.showConfirmDialog(parent, title, text, confirm_color=confirm_color)


# 自定义组件
class WindowControlButton(QPushButton):
    """自定义窗口控制按钮"""
    
    def __init__(self, button_type, parent=None):
        super().__init__(parent)
        self.button_type = button_type
        self.setFixedSize(16, 16)
        self.setCursor(Qt.CursorShape.PointingHandCursor)
        
        # 设置按钮样式
        if button_type == "close":
            bg_color = Theme.WINDOW_CLOSE
            hover_color = Theme.WINDOW_CLOSE_HOVER
        elif button_type == "minimize":
            bg_color = Theme.WINDOW_MINIMIZE
            hover_color = Theme.WINDOW_MINIMIZE_HOVER
        elif button_type == "maximize":
            bg_color = Theme.WINDOW_MAXIMIZE
            hover_color = Theme.WINDOW_MAXIMIZE_HOVER
        else:
            bg_color = Theme.CARD_LEVEL_3
            hover_color = Theme.HOVER
        
        self.setStyleSheet(f"""
            QPushButton {{
                background-color: {bg_color};
                border-radius: 8px;
                border: none;
            }}
            QPushButton:hover {{
                background-color: {hover_color};
            }}
        """)
        
    def enterEvent(self, event):
        """鼠标进入事件 - 实现简单放大效果"""
        self.setFixedSize(18, 18)
        super().enterEvent(event)
        
    def leaveEvent(self, event):
        """鼠标离开事件 - 恢复原始大小"""
        self.setFixedSize(16, 16)
        super().leaveEvent(event)


class CustomTitleBar(QWidget):
    """自定义标题栏"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent = parent
        self.setFixedHeight(50)
        self.setObjectName("customTitleBar")
        
        # 鼠标拖动相关变量
        self.pressing = False
        self.start_point = QPoint(0, 0)
        
        # 初始化UI
        self.init_ui()
        
    def init_ui(self):
        """初始化UI"""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(15, 5, 15, 5)
        
        # 窗口标题 - 不显示任何文字
        self.title_label = QLabel("")
        self.title_label.setStyleSheet(f"""
            font-size: {Theme.FONT_SIZE_TITLE};
            font-weight: bold;
            color: {Theme.TEXT_PRIMARY};
        """)
        
        # 窗口控制按钮
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(10)
        
        self.minimize_btn = WindowControlButton("minimize")
        self.maximize_btn = WindowControlButton("maximize")
        self.close_btn = WindowControlButton("close")
        
        # 连接信号
        self.minimize_btn.clicked.connect(self.parent.showMinimized)
        self.maximize_btn.clicked.connect(self.toggle_maximize)
        self.close_btn.clicked.connect(self.parent.close)
        
        buttons_layout.addWidget(self.minimize_btn)
        buttons_layout.addWidget(self.maximize_btn)
        buttons_layout.addWidget(self.close_btn)
        
        # 添加到主布局
        layout.addWidget(self.title_label)
        layout.addStretch()
        layout.addLayout(buttons_layout)
        
        # 设置样式
        self.setStyleSheet(f"""
            CustomTitleBar {{
                background-color: transparent;
            }}
        """)
    
    def toggle_maximize(self):
        """切换窗口最大化状态"""
        if self.parent.isMaximized():
            self.parent.showNormal()
        else:
            self.parent.showMaximized()
    
    def mousePressEvent(self, event):
        """鼠标按下事件"""
        if event.button() == Qt.MouseButton.LeftButton:
            self.pressing = True
            self.start_point = event.pos()
        super().mousePressEvent(event)
    
    def mouseMoveEvent(self, event):
        """鼠标移动事件"""
        if self.pressing and not self.parent.isMaximized():
            self.parent.move(self.parent.pos() + event.pos() - self.start_point)
        super().mouseMoveEvent(event)
    
    def mouseReleaseEvent(self, event):
        """鼠标释放事件"""
        self.pressing = False
        super().mouseReleaseEvent(event)
    
    def mouseDoubleClickEvent(self, event):
        """鼠标双击事件"""
        if event.button() == Qt.MouseButton.LeftButton:
            self.toggle_maximize()
        super().mouseDoubleClickEvent(event)


class StyledFrame(QFrame):
    """自定义样式的Frame"""
    def __init__(self, parent=None, has_glass_effect=False):
        super().__init__(parent)
        self.has_glass_effect = has_glass_effect
        
        if has_glass_effect:
            # 毛玻璃效果样式
            self.setStyleSheet(f"""
                StyledFrame {{
                    background-color: {Theme.GLASS_BG};
                    border-radius: {Theme.BORDER_RADIUS};
                    border: 1px solid {Theme.GLASS_BORDER};
                }}
            """)
        else:
            # 普通样式
            self.setStyleSheet(f"""
                StyledFrame {{
                    background-color: {Theme.CARD_BG};
                    border-radius: {Theme.BORDER_RADIUS};
                    border: 1px solid {Theme.BORDER};
                }}
            """)
        
        self.setObjectName("styledFrame")
        
        # 添加阴影效果
        if has_glass_effect:
            shadow = QGraphicsDropShadowEffect(self)
            shadow.setBlurRadius(15)
            shadow.setColor(QColor(0, 0, 0, 50))
            shadow.setOffset(0, 2)
            self.setGraphicsEffect(shadow)


class StyledProgressBar(QProgressBar):
    """自定义样式的进度条，重写绘制方法实现圆角效果"""
    def __init__(self, parent=None):
        super().__init__(parent)
        # 基本设置
        self.setTextVisible(True)
        self.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.setStyleSheet(f"""
            QProgressBar {{
                background-color: {Theme.CARD_LEVEL_1};
                color: {Theme.TEXT_PRIMARY};
                border-radius: 5px;
                text-align: center;
                font-weight: bold;
                border: none;
            }}
        """)
        
        # 默认颜色设置
        self._chunk_color = QColor(Theme.ACCENT)
        
        # 动画属性
        self._animation_value = 0
        self._animation = QPropertyAnimation(self, b"animationValue")
        self._animation.setEasingCurve(QEasingCurve.Type.OutQuint)  # 更平滑的动画曲线
        self._animation.setDuration(1500)  # 使用适中的动画时间
        self._animation.valueChanged.connect(self.update)
        self._animation.finished.connect(self._animation_finished)
        self._target_format = ""
        self._current_format = ""
        
        # 存储原始值，用于在UI刷新后重新应用动画
        self._original_value = 0
        
        # 添加持久值存储，不会被reset_without_animation重置
        self._persistent_value = 0
        self._persistent_max = 0
        self._persistent_format = ""
        
        # 流光动画属性
        self._glow_position = 0.0  # 流光位置 (0.0 到 1.0)
        self._glow_width = 0.2     # 流光宽度 (占总宽度的比例)
        self._glow_opacity = 0.4   # 流光不透明度
        
        # 创建流光动画
        self._glow_animation = QPropertyAnimation(self, b"glowPosition")
        self._glow_animation.setDuration(2200)  # 更长的动画时间，更流畅的感觉
        self._glow_animation.setStartValue(-self._glow_width)
        self._glow_animation.setEndValue(1.0 + self._glow_width)
        self._glow_animation.setEasingCurve(QEasingCurve.Type.InOutSine)  # 使用平滑的曲线
        self._glow_animation.finished.connect(self._restart_glow_animation)
        self._glow_animation.start()
        
        # 确保初始值设置
        QTimer.singleShot(0, lambda: self._init_animation_value())
    
    def reset_without_animation(self, store_original=True):
        """在不触发动画的情况下重置进度条
        
        Args:
            store_original: 是否存储原始值以便后续恢复，默认为True
        """
        try:
            # 如果需要，保存当前值
            if store_original:
                self._original_value = self.value()
                
                # 注意：不修改_persistent_value，它是持久存储，不受重置影响
            
            # 停止任何正在运行的动画
            if self._animation.state() == QAbstractAnimation.State.Running:
                self._animation.stop()
            
            # 直接修改内部状态，跳过动画
            self.blockSignals(True)
            self._animation_value = 0
            # 使用父类的setValue方法，避免触发我们自定义的动画逻辑
            QProgressBar.setValue(self, 0)
            self.blockSignals(False)
            self.update()  # 强制更新UI
        except Exception as e:
            print(f"重置进度条失败: {str(e)}")
    
    def restore_original_value(self):
        """恢复原始值并启动动画"""
        try:
            # 检查是否已经在动画中，如果是则不要重复启动
            if self._animation.state() == QAbstractAnimation.State.Running:
                print(f"进度条已在动画中，跳过重复恢复")
                return
                
            # 初始化值变量
            value_to_use = 0
            max_to_use = 0
            format_to_use = ""
            
            # 优先级1：使用持久值存储（如果有效）
            if hasattr(self, '_persistent_value') and self._persistent_value > 0:
                value_to_use = self._persistent_value
                max_to_use = self._persistent_max if self._persistent_max > 0 else self.maximum()
                format_to_use = self._persistent_format if self._persistent_format else ""
                print(f"使用持久值: {value_to_use}/{max_to_use}, format={format_to_use}")
            
            # 优先级2：使用原始值存储（如果有效且持久值无效）
            elif hasattr(self, '_original_value') and self._original_value > 0:
                value_to_use = self._original_value
                max_to_use = self.maximum()
                format_to_use = ""
                print(f"使用原始值: {value_to_use}/{max_to_use}")
            
            # 优先级3：尝试从当前格式字符串中提取值（如果前两者都无效）
            else:
                try:
                    import re
                    current_format = self.format()
                    
                    # 尝试匹配"数字/数字"格式（如"646/500"）
                    match = re.match(r"^(\d+)/(\d+)$", current_format)
                    if match:
                        value_to_use = int(match.group(1))
                        max_to_use = int(match.group(2))
                        format_to_use = current_format
                        print(f"从格式提取值: {value_to_use}/{max_to_use}")
                except Exception as e:
                    print(f"从格式提取值失败: {str(e)}")
            
            # 检查是否有有效值可用于恢复
            if value_to_use > 0:
                # 设置最大值（如果需要）
                if max_to_use > 0 and max_to_use != self.maximum():
                    self.setRange(0, max_to_use)
                
                # 设置值并启动动画
                self.setValue(value_to_use)
                
                # 如果有格式字符串，也恢复它
                if format_to_use:
                    self.setFormat(format_to_use)
            else:
                print(f"未找到可恢复的值")
        except Exception as e:
            print(f"恢复进度条原始值时出错: {str(e)}")
    
    def _init_animation_value(self):
        """初始化动画值为当前值"""
        self._animation_value = self.value()
        self.update()
    
    def _animation_finished(self):
        """动画完成后的回调"""
        try:
            # 获取整数目标值，避免浮点数和整数转换的舍入差异
            target_int_value = int(self._animation.endValue())
            
            # 在设置值之前停止可能的任何事件处理
            self.blockSignals(True)
            
            # 将动画值和实际值完全同步为相同的整数值
            self._animation_value = float(target_int_value)  # 使用精确的float值
            super().setValue(target_int_value)
            
            # 恢复信号处理
            self.blockSignals(False)
            
            # 强制更新绘制，确保显示正确
            self.update()
            
            if self._target_format:
                self._current_format = self._target_format
                super().setFormat(self._target_format)
                self._target_format = ""
        except Exception as e:
            print(f"动画完成回调出错: {str(e)}")
            # 发生错误时，尝试简单同步值
            self._animation_value = self.value()
            self.update()
    
    def getAnimationValue(self):
        """获取动画值"""
        return self._animation_value
    
    def setAnimationValue(self, value):
        """设置动画值"""
        # 确保动画值在合理范围内
        min_val = float(self.minimum())
        max_val = float(self.maximum())
        value = max(min_val, min(max_val, value))
        
        self._animation_value = value
        self.update()  # 更新绘制
    
    # 定义动画属性
    animationValue = pyqtProperty(float, getAnimationValue, setAnimationValue)
    
    def setFormat(self, format_str):
        """重写设置格式方法，添加动画支持"""
        self._current_format = format_str  # 立即更新当前格式，供绘制使用
        
        # 存储非加载状态的格式
        if format_str and not format_str.startswith("加载中"):
            self._persistent_format = format_str
        
        if self._animation.state() == QAbstractAnimation.State.Running:
            # 如果动画正在运行，保存目标格式以便在动画完成后设置
            self._target_format = format_str
        else:
            # 否则直接设置
            super().setFormat(format_str)
    
    def setValue(self, value):
        """重写setValue方法，添加动画效果"""
        try:
            if self._animation.state() == QAbstractAnimation.State.Running:
                self._animation.stop()
            
            # 保存当前动画值为起点
            current_value = self.value()
            
            # 将值转换为整数，避免精度问题
            value = int(value)
            
            # 更新持久值存储 - 始终保存最新的非零值
            if value > 0:
                self._persistent_value = value
                self._persistent_max = self.maximum()
                # 如果有格式字符串，也保存它
                current_format = self.format()
                if current_format and not current_format.startswith("加载中"):
                    self._persistent_format = current_format
            
            # 设置实际值（用于保存状态）
            super().setValue(value)
            
            # 如果值相同，不启动动画
            if current_value == value:
                self._animation_value = float(value)  # 保持精确的float值
                self.update()
                return
            
            # 确保最小动画差异
            start_value = current_value
            end_value = value
            
            # 当值变化很小时，仍然保持一个最小的动画效果
            if abs(end_value - start_value) < 1 and end_value != start_value:
                if end_value > start_value:
                    start_value = max(0, end_value - 1)
                else:
                    start_value = min(self.maximum(), end_value + 1)
            
            # 设置当前动画值
            self._animation_value = float(start_value)
            
            # 设置动画
            self._animation.setStartValue(float(start_value))
            self._animation.setEndValue(float(end_value))  # 确保使用浮点数避免精度问题
            self._animation.start()
        except Exception as e:
            print(f"设置进度条值出错: {str(e)}")
            # 如果出错，尝试直接设置值而不使用动画
            try:
                self._animation_value = float(value)
                super().setValue(value)
                self.update()
            except:
                pass
    
    def paintEvent(self, event):
        # 创建绘制器
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)  # 抗锯齿
        
        # 获取进度条尺寸
        width = self.width()
        height = self.height()
        radius = height / 2  # 圆角半径为高度的一半，创建胶囊形状
        
        # 绘制背景 - 胶囊形状
        bg_color = QColor(Theme.CARD_LEVEL_1)
        painter.setPen(Qt.PenStyle.NoPen)
        painter.setBrush(bg_color)
        painter.drawRoundedRect(0, 0, width, height, radius, radius)
        
        try:
            # 计算填充宽度 - 使用动画值而不是实际值
            # 确保进度在有效范围内
            min_val = float(self.minimum())
            max_val = float(self.maximum())
            range_val = max_val - min_val
            
            if range_val <= 0:
                progress = 0
            else:
                progress = (self._animation_value - min_val) / range_val
                progress = max(0.0, min(1.0, progress))
            
            # 使用round代替int，更准确的舍入
            chunk_width = round(width * progress)
            
            # 绘制填充部分 - 也是胶囊形状
            if chunk_width > 0:
                painter.setBrush(self._chunk_color)
                # 极小值特殊处理 - 绘制一个小圆形
                if chunk_width <= height:
                    # 绘制一个小于或等于高度的圆形
                    painter.drawRoundedRect(0, 0, height, height, radius, radius)
                    # 如果宽度小于高度，裁剪掉多余部分
                    if chunk_width < height:
                        # 创建一个矩形覆盖超出部分
                        painter.setBrush(bg_color)
                        painter.drawRect(chunk_width, 0, height - chunk_width, height)
                else:
                    # 正常绘制胶囊形状
                    painter.drawRoundedRect(0, 0, chunk_width, height, radius, radius)
                
                # 绘制流光效果（仅在填充部分上）
                if progress > 0:
                    # 创建裁剪区域，确保流光仅在进度条填充区域内显示
                    painter.setClipRect(0, 0, chunk_width, height)
                    
                    # 计算流光位置和宽度
                    glow_start = width * (self._glow_position - self._glow_width)
                    glow_end = width * (self._glow_position + self._glow_width)
                    
                    # 创建线性渐变作为流光效果
                    gradient = QLinearGradient(glow_start, 0, glow_end, 0)
                    base_color = QColor(self._chunk_color)
                    highlight_color = QColor(255, 255, 255, int(255 * self._glow_opacity))
                    
                    # 创建半透明的中间颜色，用于羽化效果
                    mid_opacity_1 = self._glow_opacity * 0.3
                    mid_opacity_2 = self._glow_opacity * 0.7
                    mid_color_1 = QColor(255, 255, 255, int(255 * mid_opacity_1))
                    mid_color_2 = QColor(255, 255, 255, int(255 * mid_opacity_2))
                    
                    # 设置渐变颜色，增加更多点实现平滑羽化
                    gradient.setColorAt(0.0, base_color)
                    gradient.setColorAt(0.2, mid_color_1)  # 添加过渡色
                    gradient.setColorAt(0.35, mid_color_2)  # 添加过渡色
                    gradient.setColorAt(0.5, highlight_color)  # 中心点
                    gradient.setColorAt(0.65, mid_color_2)  # 添加过渡色
                    gradient.setColorAt(0.8, mid_color_1)  # 添加过渡色
                    gradient.setColorAt(1.0, base_color)
                    
                    # 应用渐变并绘制
                    painter.setBrush(QBrush(gradient))
                    
                    # 重新绘制填充区域，但使用渐变
                    if chunk_width <= height:
                        painter.drawRoundedRect(0, 0, height, height, radius, radius)
                        if chunk_width < height:
                            painter.setBrush(bg_color)
                            painter.drawRect(chunk_width, 0, height - chunk_width, height)
                    else:
                        painter.drawRoundedRect(0, 0, chunk_width, height, radius, radius)
                    
                    # 清除裁剪区域
                    painter.setClipping(False)
        except Exception as e:
            print(f"进度条绘制错误: {str(e)}")
        
        # 绘制文本 - 使用当前格式而不是内部格式
        painter.setPen(QColor(Theme.TEXT_PRIMARY))
        painter.setFont(self.font())
        display_text = self._current_format if self._current_format else self.text()
        painter.drawText(self.rect(), Qt.AlignmentFlag.AlignCenter, display_text)
        
        # 结束绘制
        painter.end()
    
    def setChunkColor(self, color):
        """设置填充颜色"""
        if isinstance(color, str):
            self._chunk_color = QColor(color)
        else:
            self._chunk_color = color
        self.update()  # 更新绘制

    def getGlowPosition(self):
        """获取流光位置属性"""
        return self._glow_position
    
    def setGlowPosition(self, position):
        """设置流光位置属性"""
        self._glow_position = position
        self.update()  # 触发重绘
    
    # 定义流光位置属性
    glowPosition = pyqtProperty(float, getGlowPosition, setGlowPosition)
    
    def _restart_glow_animation(self):
        """流光动画完成后重新启动"""
        self._glow_animation.start()

    def hideEvent(self, event):
        """在组件隐藏时暂停动画以节省资源"""
        super().hideEvent(event)
        if hasattr(self, '_glow_animation') and self._glow_animation.state() == QAbstractAnimation.State.Running:
            self._glow_animation.pause()
            
    def showEvent(self, event):
        """组件显示时重新启动动画"""
        super().showEvent(event)
        if hasattr(self, '_glow_animation') and self._glow_animation.state() == QAbstractAnimation.State.Paused:
            self._glow_animation.resume()
            
    def closeEvent(self, event):
        """组件关闭时清理资源"""
        if hasattr(self, '_glow_animation'):
            self._glow_animation.stop()
        super().closeEvent(event)


class AnimatedNumberLabel(QLabel):
    """带有动画效果的数字标签"""
    
    def __init__(self, parent=None, prefix="", suffix="", duration=1500):
        """
        初始化数字动画标签
        
        Args:
            parent: 父窗口部件
            prefix: 数字前缀文本，如"剩余时间: "
            suffix: 数字后缀文本，如"天"
            duration: 动画持续时间(毫秒)
        """
        super().__init__(parent)
        self.prefix = prefix
        self.suffix = suffix
        self.duration = duration
        
        # 动画相关变量
        self._current_value = 0
        self._target_value = 0
        self._animation_value = 0
        self._animation = QPropertyAnimation(self, b"animationValue")
        self._animation.setEasingCurve(QEasingCurve.Type.OutQuad)  # 使用更平滑的曲线
        self._animation.setDuration(self.duration)
        self._animation.valueChanged.connect(self._update_text)
        self._animation.finished.connect(self._on_animation_finished)
        
        # 调试输出
        self._last_value = -1
        
        # 特殊文本模式（非数字状态）
        self._special_text = None
        
        # 默认样式
        self.setStyleSheet(f"""
            font-size: {Theme.FONT_SIZE_NORMAL}; 
            padding: 8px 12px;  /* 修改为与QLabel相同的padding */
            background-color: transparent;
            border-radius: {Theme.BORDER_RADIUS_SMALL};
            font-weight: bold;
            color: {Theme.SUCCESS};
        """)
        
        # 确保属性动画完整显示
        QTimer.singleShot(100, self._init_animation)
    
    def reset_animation(self):
        """重置并重新启动动画"""
        try:
            # 尝试重置并重启动画，不论是否有特殊文本
            # 停止当前可能在运行的动画
            if self._animation.state() == QAbstractAnimation.State.Running:
                self._animation.stop()
            
            # 只在有目标值且不是特殊文本时执行动画
            if not self._special_text and self._target_value > 0:
                # 重置动画值为0并设置从0到目标值的动画
                self._animation_value = 0
                self._current_value = 0
                self._last_value = -1  # 重置上次值以确保更新UI
                
                # 配置动画
                self._animation.setStartValue(0.0)
                self._animation.setEndValue(self._target_value)
                
                # 根据数值大小调整动画持续时间，大数值动画时间更长
                adjusted_duration = min(self.duration, max(1000, int(self._target_value * 30)))
                self._animation.setDuration(adjusted_duration)
                
                # 更新UI以显示初始值
                self._update_text()
                
                # 启动动画
                QTimer.singleShot(50, self._animation.start)  # 短暂延迟确保UI准备好
            elif self._special_text:
                # 如果是特殊文本，仅更新显示
                self._update_text()
        except Exception as e:
            print(f"重置数字标签动画出错: {str(e)}")
            # 尝试恢复到可用状态
            try:
                if self._target_value > 0:
                    self._current_value = self._target_value
                    self._update_text()
            except:
                pass
    
    def _init_animation(self):
        """初始化动画设置"""
        try:
            # 仅在有具体值时初始化
            if not self._special_text and self._target_value > 0:
                self.setValue(self._target_value, animate=True)
        except Exception as e:
            print(f"初始化动画出错: {str(e)}")
    
    def _on_animation_finished(self):
        """动画完成时的回调"""
        # 确保最终值是精确的目标值
        self._animation_value = self._target_value
        self._current_value = self._target_value
        self._update_text()  # 最后一次更新
    
    def getAnimationValue(self):
        """获取动画当前值"""
        return self._animation_value
    
    def setAnimationValue(self, value):
        """设置动画当前值并更新显示"""
        self._animation_value = value
        self._update_text()
    
    # 定义动画属性
    animationValue = pyqtProperty(float, getAnimationValue, setAnimationValue)
    
    def _update_text(self):
        """根据当前动画值更新文本"""
        try:
            if self._special_text:
                # 如果有特殊文本，直接显示，不添加后缀
                # 检查特殊文本是否包含"已过期"或"加载中"
                if "已过期" in self._special_text or "加载中" in self._special_text or "尊贵的有钱人Pro用户" in self._special_text:
                    # 对于"已过期"、"加载中"和"尊贵的有钱人Pro用户"的情况，不添加后缀
                    self.setText(f"{self.prefix}{self._special_text}")
                else:
                    # 其他特殊文本仍然添加后缀
                    self.setText(f"{self.prefix}{self._special_text}{self.suffix}")
            else:
                # 否则显示动画的当前值（四舍五入到整数）
                value = round(self._animation_value)
                # 仅在值变化时更新UI，减少重绘
                if value != self._last_value:
                    self._last_value = value
                    self.setText(f"{self.prefix}{value}{self.suffix}")
        except Exception as e:
            print(f"更新动画标签文本出错: {str(e)}")
    
    def setValue(self, value, animate=True):
        """
        设置标签的值
        
        Args:
            value: 数值或特殊字符串(如"已过期")
            animate: 是否使用动画效果
        """
        try:
            # 停止任何正在运行的动画
            if self._animation.state() == QAbstractAnimation.State.Running:
                self._animation.stop()
            
            # 处理非数字值
            if not isinstance(value, (int, float)) or value < 0:
                self._special_text = str(value)
                self._animation_value = 0
                self._current_value = 0
                self._target_value = 0
                self._update_text()
                return
            
            # 清除特殊文本状态
            self._special_text = None
            
            # 设置目标值
            self._target_value = float(value)
            
            # 如果当前值未初始化，则从0开始
            if self._current_value <= 0:
                self._current_value = 0
            
            if animate and self._current_value != self._target_value:
                # 设置动画，总是从0开始递增而不是从当前值，这样效果更明显
                self._animation.setStartValue(0.0)
                self._animation.setEndValue(self._target_value)
                
                # 根据数值大小调整动画持续时间，大数值动画时间更长
                adjusted_duration = min(self.duration, max(1000, int(self._target_value * 30)))
                self._animation.setDuration(adjusted_duration)
                
                # 启动动画
                QTimer.singleShot(10, self._animation.start)  # 延迟启动以确保UI准备好
            else:
                # 不使用动画，直接设置值
                self._animation_value = self._target_value
                self._current_value = self._target_value
                self._update_text()
        except Exception as e:
            print(f"设置动画数字标签值出错: {str(e)}")
            # 如果出错，尝试直接设置文本
            self._special_text = str(value)
            self._update_text()
    
    def setColor(self, color):
        """设置文本颜色"""
        self.setStyleSheet(f"""
            font-size: {Theme.FONT_SIZE_NORMAL}; 
            padding: 8px 12px;  /* 修改为与QLabel相同的padding */
            background-color: transparent;
            border-radius: {Theme.BORDER_RADIUS_SMALL};
            font-weight: bold;
            color: {color};
        """)
        
    def setSpecialText(self, text):
        """设置特殊文本（非数字值）"""
        self._special_text = text
        self._update_text()


# 账户信息行类，用于账户管理页面
class AccountRowWidget(QWidget):
    """账户信息行组件"""
    
    # 定义信号
    switch_account_signal = pyqtSignal(dict)
    delete_account_signal = pyqtSignal(str)
    
    def __init__(self, account_data, is_current=False, parent=None):
        super().__init__(parent)
        self.account_data = account_data
        self.is_current = is_current
        self.is_loading = False
        self.init_ui()
    
    def update_account_data(self, new_account_data):
        """更新账户数据
        
        Args:
            new_account_data: 新的账户数据
        """
        self.account_data = new_account_data
        
        # 更新UI显示
        email = self.account_data.get("email", "")
        account_type = self.account_data.get("account_type", "未知")
        username = self.account_data.get("username", "未知")
        
        # 更新标签文本
        if hasattr(self, 'email_label'):
            self.email_label.setText(email)
        
        if hasattr(self, 'type_label'):
            self.type_label.setText(f"类型：{account_type}")
            
        if hasattr(self, 'username_label'):
            self.username_label.setText(f"用户名：{username}")
    
    def init_ui(self):
        """初始化UI"""
        self.setFixedHeight(90)  # 增加行高，使布局更宽松
        
        # 主卡片效果
        bg_color = Theme.CARD_LEVEL_2 if self.is_current else Theme.CARD_LEVEL_1
        border_style = f"border-left: 4px solid {Theme.ACCENT};" if self.is_current else ""
        
        self.setStyleSheet(f"""
            AccountRowWidget {{
                background-color: {bg_color};
                border-radius: {Theme.BORDER_RADIUS_SMALL};
                {border_style}
                margin: 4px 2px;
                padding: 0px;
            }}
            
            AccountRowWidget:hover {{
                background-color: {Theme.HOVER};
            }}
            
            QLabel {{
                padding: 0px;
                margin: 0px;
            }}
        """)
        
        # 主布局
        self.main_layout = QHBoxLayout(self)
        self.main_layout.setContentsMargins(15, 12, 15, 12)
        self.main_layout.setSpacing(5)  # 减少组件间的间距
        
        # 左侧 - 账户信息区域
        left_container = QWidget()
        left_container.setStyleSheet("background: transparent;")
        left_layout = QVBoxLayout(left_container)
        left_layout.setContentsMargins(0, 0, 0, 0)
        left_layout.setSpacing(6)
        
        # 邮箱信息
        self.email_label = QLabel(self.account_data.get("email", "未知"))
        self.email_label.setTextFormat(Qt.TextFormat.PlainText)
        self.email_label.setTextInteractionFlags(Qt.TextInteractionFlag.TextSelectableByMouse)
        self.email_label.setStyleSheet(f"""
            color: {Theme.ACCENT if self.is_current else Theme.TEXT_PRIMARY}; 
            font-weight: {'bold' if self.is_current else 'normal'};
            font-size: {Theme.FONT_SIZE_NORMAL};
        """)
        left_layout.addWidget(self.email_label)
        
        # 创建注册时间布局
        time_layout = QHBoxLayout()
        time_layout.setSpacing(6)
        time_layout.setContentsMargins(0, 0, 0, 0)
        
        register_time_label = QLabel("注册时间:")
        register_time_label.setStyleSheet(f"color: {Theme.TEXT_SECONDARY}; font-size: {Theme.FONT_SIZE_SMALL};")
        time_layout.addWidget(register_time_label)
        
        self.register_time_label = QLabel("--")
        self.register_time_label.setStyleSheet(f"color: {Theme.TEXT_PRIMARY}; font-size: {Theme.FONT_SIZE_SMALL};")
        time_layout.addWidget(self.register_time_label)
        
        time_layout.addStretch()
        
        # 添加时间布局到左侧容器
        left_layout.addLayout(time_layout)
        
        # 中间部分 - 包含剩余天数和进度条
        center_container = QWidget()
        center_container.setStyleSheet("background: transparent;")
        center_layout = QVBoxLayout(center_container)
        center_layout.setContentsMargins(0, 0, 0, 0)
        center_layout.setSpacing(6)
        
        # 剩余天数行
        days_layout = QHBoxLayout()
        days_layout.setSpacing(0)  # 将间距从6改为0，减少标签与数值之间的距离
        days_layout.setContentsMargins(0, 0, 0, 0)
        
        days_title = QLabel("剩余天数:")
        days_title.setStyleSheet(f"color: {Theme.TEXT_SECONDARY}; font-size: {Theme.FONT_SIZE_SMALL};")
        days_layout.addWidget(days_title)
        
        # 使用动画数字标签显示剩余天数
        self.remaining_days_label = AnimatedNumberLabel(prefix="", suffix="天", duration=2000)
        self.remaining_days_label.setSpecialText("未知")
        self.remaining_days_label.setColor(Theme.SUCCESS)
        self.remaining_days_label.setStyleSheet(f"""
            font-size: {Theme.FONT_SIZE_SMALL}; 
            padding: 0px;
            background-color: transparent;
            border-radius: {Theme.BORDER_RADIUS_SMALL};
            font-weight: bold;
            color: {Theme.SUCCESS};
        """)
        days_layout.addWidget(self.remaining_days_label)
        
        days_layout.addStretch()
        
        # 添加剩余天数布局到中间容器
        center_layout.addLayout(days_layout)
        
        # 额度行
        quota_layout = QHBoxLayout()
        quota_layout.setSpacing(6)
        quota_layout.setContentsMargins(0, 0, 0, 0)
        quota_layout.setAlignment(Qt.AlignmentFlag.AlignLeft)  # 设置为左对齐
        
        quota_title = QLabel("高级模型:")
        quota_title.setStyleSheet(f"color: {Theme.TEXT_SECONDARY}; font-size: {Theme.FONT_SIZE_SMALL};")
        quota_layout.addWidget(quota_title)
        
        # 创建动画进度条，使用更短的动画时间以保持UI响应性
        self.quota_bar = AnimatedProgressBar(text_duration=1500, value_duration=1000)
        self.quota_bar.setMinimumHeight(18)
        self.quota_bar.setMaximumHeight(18)
        self.quota_bar.setFixedWidth(150)    # 减小宽度到150像素
        self.quota_bar.setRange(0, 150)
        self.quota_bar.setValue(0)
        self.quota_bar.setFormat("未知")
        
        quota_layout.addWidget(self.quota_bar, 0)  # 使用0作为伸展因子，不会拉伸
        quota_layout.addStretch(1)  # 添加伸展空间，将前面的组件推到左侧
        
        # 添加额度布局到中间容器
        center_layout.addLayout(quota_layout)
        
        # 右侧 - 操作按钮
        right_container = QWidget()
        right_container.setStyleSheet("background: transparent;")
        right_layout = QVBoxLayout(right_container)  # 改为垂直布局
        right_layout.setContentsMargins(0, 16, 0, 0)  # 增加顶部边距，向下移动按钮
        right_layout.setSpacing(8)  # 设置垂直间距
        right_layout.setAlignment(Qt.AlignmentFlag.AlignLeft)  # 左对齐
        
        # 水平布局用于放置两个按钮
        buttons_layout = QHBoxLayout()
        buttons_layout.setContentsMargins(15, 0, 0, 0)  # 增加左侧边距
        buttons_layout.setSpacing(5)  # 减少间距
        buttons_layout.setAlignment(Qt.AlignmentFlag.AlignLeft | Qt.AlignmentFlag.AlignVCenter)  # 左对齐
        
        # 根据当前用户状态显示不同的按钮
        if self.is_current:
            # 当前账户 - 改为禁用的更换按钮
            self.switch_btn = QPushButton("更换")
            self.switch_btn.setFixedSize(65, 28)  # 稍微增加按钮尺寸
            self.switch_btn.setEnabled(False)  # 设置为禁用状态
            self.switch_btn.setCursor(Qt.CursorShape.ArrowCursor)
            self.switch_btn.setStyleSheet(f"""
            QPushButton {{
                    background-color: {Theme.DISABLED};
                    color: {Theme.TEXT_SECONDARY};
                    border: none;
                border-radius: {Theme.BORDER_RADIUS_SMALL};
                    font-weight: normal;
                    font-size: 11px;
                    font-family: 'Microsoft YaHei UI', 'SimHei', sans-serif;
                    padding: 0px;
                    letter-spacing: 0px;
                }}
            """)
            buttons_layout.addWidget(self.switch_btn)
        else:
            # 切换按钮
            self.switch_btn = QPushButton("更换")
            self.switch_btn.setFixedSize(65, 28)  # 稍微增加按钮尺寸
            self.switch_btn.setCursor(Qt.CursorShape.PointingHandCursor)
            self.switch_btn.setStyleSheet(f"""
                QPushButton {{
                    background-color: {Theme.ACCENT};
                    color: {Theme.PRIMARY};
                    border: none;
                    border-radius: {Theme.BORDER_RADIUS_SMALL};
                    font-weight: normal;
                    font-size: 11px;
                    font-family: 'Microsoft YaHei UI', 'SimHei', sans-serif;
                    padding: 0px;
                    letter-spacing: 0px;
            }}
            QPushButton:hover {{
                    background-color: {Theme.ACCENT_HOVER};
            }}
            QPushButton:pressed {{
                    background-color: #238970;
                }}
            """)
            self.switch_btn.clicked.connect(self.on_switch_clicked)
            buttons_layout.addWidget(self.switch_btn)
        
        # 删除按钮
        self.delete_btn = QPushButton("删除")
        self.delete_btn.setFixedSize(65, 28)  # 稍微增加按钮尺寸
        
        if self.is_current:
            # 当前账户的删除按钮设为禁用状态
            self.delete_btn.setEnabled(False)
            self.delete_btn.setCursor(Qt.CursorShape.ArrowCursor)
            self.delete_btn.setStyleSheet(f"""
                QPushButton {{
                background-color: {Theme.DISABLED};
                color: {Theme.TEXT_SECONDARY};
                    border: none;
                    border-radius: {Theme.BORDER_RADIUS_SMALL};
                    font-weight: normal;
                    font-size: 11px;
                    font-family: 'Microsoft YaHei UI', 'SimHei', sans-serif;
                    padding: 0px;
                    letter-spacing: 0px;
                }}
            """)
        else:
            # 非当前账户的删除按钮
            self.delete_btn.setCursor(Qt.CursorShape.PointingHandCursor)
            self.delete_btn.setStyleSheet(f"""
            QPushButton {{
                    background-color: transparent;
                color: {Theme.ERROR};
                    border: 1px solid {Theme.ERROR};
                border-radius: {Theme.BORDER_RADIUS_SMALL};
                    font-weight: normal;
                    font-size: 11px;
                    font-family: 'Microsoft YaHei UI', 'SimHei', sans-serif;
                    padding: 0px;
                    letter-spacing: 0px;
            }}
            QPushButton:hover {{
                background-color: {Theme.ERROR};
                color: white;
            }}
            QPushButton:pressed {{
                background-color: #A8414E;
                color: white;
                }}
            """)
        self.delete_btn.clicked.connect(self.on_delete_clicked)
        
        buttons_layout.addWidget(self.delete_btn)
        
        # 将按钮布局添加到右侧容器
        right_layout.addLayout(buttons_layout)
        
        # 添加到主布局，调整比例使中间部分更靠近左侧
        self.main_layout.addWidget(left_container, 4)  # 增加左侧比例
        self.main_layout.addWidget(center_container, 4)  # 增加中间比例
        self.main_layout.addWidget(right_container, 3)  # 增加右侧比例，确保按钮显示完整
    
    def enterEvent(self, event):
        """鼠标进入控件区域"""
        self.setCursor(Qt.CursorShape.PointingHandCursor)
        super().enterEvent(event)
    
    def leaveEvent(self, event):
        """鼠标离开控件区域"""
        self.setCursor(Qt.CursorShape.ArrowCursor)
        super().leaveEvent(event)
    
    def on_switch_clicked(self):
        """当点击更换按钮时触发"""
        self.switch_account_signal.emit(self.account_data)
    
    def on_delete_clicked(self):
        """当点击删除按钮时触发"""
        self.delete_account_signal.emit(self.account_data.get("email", ""))
    
    def set_loading_state(self, is_loading):
        """设置加载状态"""
        self.is_loading = is_loading
        if is_loading:
            if hasattr(self, 'quota_bar') and self.quota_bar is not None:
                self.quota_bar.setFormat("加载中...")
                self.quota_bar.setValue(0)  # 清除进度条填充，重置为0
            
            # 禁用按钮
            if not self.is_current and hasattr(self, 'switch_btn'):
                self.switch_btn.setEnabled(False)
            if not self.is_current and hasattr(self, 'delete_btn'):
                self.delete_btn.setEnabled(False)
        else:
            # 恢复按钮状态，但当前账户的按钮始终保持禁用
            if not self.is_current and hasattr(self, 'switch_btn'):
                self.switch_btn.setEnabled(True)
            if not self.is_current and hasattr(self, 'delete_btn'):
                self.delete_btn.setEnabled(True)
        
    def update_quota(self, quota_data):
        """更新账户额度"""
        try:
            self.set_loading_state(False)  # 停止加载动画
            
            # 更新注册时间和剩余天数
            start_of_month = quota_data.get("startOfMonth")
            
            # 如果有真实注册时间，更新注册时间显示并重新计算剩余天数
            if start_of_month:
                try:
                    # 解析ISO 8601格式的时间字符串
                    from datetime import datetime, timedelta, timezone
                    from dateutil import parser
                    
                    # 使用dateutil解析器兼容多种时间格式
                    reg_date = parser.parse(start_of_month)
                    
                    # 获取当前时间
                    now = datetime.now(timezone.utc).replace(tzinfo=None)
                    
                    # 时区处理 - 转换为中国时间 (UTC+8)
                    china_tz = timezone(timedelta(hours=8))
                    reg_date_china = reg_date.astimezone(china_tz)
                    
                    # 格式化注册时间显示 (使用中国时间)
                    formatted_time = reg_date_china.strftime("%Y-%m-%d %H:%M:%S")
                    if hasattr(self, 'register_time_label'):
                        self.register_time_label.setText(formatted_time)
                    
                    # 去除时区信息以便比较
                    reg_date = reg_date.replace(tzinfo=None)
                    
                    # 计算总试用期限（14天）
                    trial_period = timedelta(days=14)
                    expiry_date = reg_date + trial_period
                    
                    # 计算已经使用的时间
                    used_time = now - reg_date
                    used_days = used_time.days
                    remaining_days_float = 14 - used_days - (used_time.seconds / 86400)  # 精确到小数
                    
                    # 如果已过期或剩余不足1天
                    if now > expiry_date or remaining_days_float < 1:
                        remaining_days = "已过期"
                        color = Theme.ERROR
                        display_text = remaining_days
                        if hasattr(self, 'remaining_days_label'):
                            self.remaining_days_label.setSpecialText(display_text)
                            self.remaining_days_label.setColor(color)
                    else:
                        # 计算剩余天数（总试用期14天 - 已使用天数）
                        remaining_days = 14 - used_days
                        color = Theme.SUCCESS
                        if hasattr(self, 'remaining_days_label'):
                            self.remaining_days_label.setValue(remaining_days, animate=True)
                            self.remaining_days_label.setColor(color)
                    
                    # 更新显示
                    if hasattr(self, 'remaining_days_label'):
                        self.remaining_days_label.setColor(color)
                    
                    # 保存到账户数据
                    self.account_data["real_register_time"] = start_of_month
                    self.account_data["real_remaining_days"] = remaining_days
                except Exception as e:
                    print(f"计算剩余天数时出错: {str(e)}")
                    if hasattr(self, 'register_time_label'):
                        self.register_time_label.setText("--")
                    if hasattr(self, 'remaining_days_label'):
                        self.remaining_days_label.setText("未知")
            else:
                # 没有真实注册时间
                if hasattr(self, 'register_time_label'):
                    self.register_time_label.setText("--")
                if hasattr(self, 'remaining_days_label'):
                    self.remaining_days_label.setText("未知")
            
            # 高级模型 (GPT-4)
            gpt4_data = quota_data.get("gpt-4", {})
            usage = gpt4_data.get("numRequestsTotal", 0)
            max_usage = gpt4_data.get("maxRequestUsage", 150)
            
            # 检查高级模型使用上限，当≥500时显示"尊贵的有钱人Pro用户"
            if max_usage >= 500:
                # 临时保存原始前缀并设置为空
                original_prefix = self.remaining_days_label.prefix
                self.remaining_days_label.prefix = ""
                self.remaining_days_label.setSpecialText("尊贵的有钱人Pro用户")
                self.remaining_days_label.setColor(Theme.GOLD)  # 使用金色突出显示
            
            if max_usage is None:
                max_usage = 150
            
            if hasattr(self, 'quota_bar') and self.quota_bar is not None:
                self.quota_bar.setRange(0, max_usage)
                self.quota_bar.setValue(usage)  # 显示已使用量
                
                # 根据使用情况设置不同颜色
                usage_percent = usage / max_usage if max_usage > 0 else 0
                if usage_percent > 0.9:
                    # 已用超过90%，显示红色警告
                    self.quota_bar.setChunkColor(Theme.ERROR)
                elif usage_percent > 0.7:
                    # 已用超过70%，显示黄色警告
                    self.quota_bar.setChunkColor(Theme.WARNING)
                else:
                    # 默认绿色
                    self.quota_bar.setChunkColor(Theme.ACCENT)
                
                # 设置显示格式
            if max_usage > 0:
                self.quota_bar.setFormat(f"{usage}/{max_usage}")
            else:
                self.quota_bar.setFormat("无限制")
            
            # 更新账户数据中的额度
            self.account_data["real_usage"] = usage
            self.account_data["real_max_usage"] = max_usage
            
        except Exception as e:
            print(f"更新账户额度时出错: {str(e)}")
            if hasattr(self, 'quota_bar') and self.quota_bar is not None:
                self.quota_bar.setFormat("获取失败")
            if hasattr(self, 'register_time_label'):
                self.register_time_label.setText("--")
            if hasattr(self, 'remaining_days_label'):
                self.remaining_days_label.setSpecialText("未知")
                self.remaining_days_label.setColor(Theme.TEXT_SECONDARY)
    
    def _update_usage_ui(self, quota_data):
        """更新使用额度UI"""
        # 直接调用实现方法，不再使用QTimer延迟
        self._update_usage_ui_impl(quota_data)
    
    def _update_usage_ui_impl(self, quota_data):
        """在主线程中实际更新UI"""
        try:
            # 更新注册时间和剩余天数
            start_of_month = quota_data.get("startOfMonth")
            
            # 如果有真实注册时间，更新注册时间显示并重新计算剩余天数
            if start_of_month:
                try:
                    # 解析ISO 8601格式的时间字符串
                    from datetime import datetime, timedelta, timezone
                    from dateutil import parser
                    
                    # 使用dateutil解析器兼容多种时间格式
                    reg_date = parser.parse(start_of_month)
                    
                    # 获取当前时间
                    now = datetime.now(timezone.utc).replace(tzinfo=None)
                    
                    # 时区处理 - 转换为中国时间 (UTC+8)
                    china_tz = timezone(timedelta(hours=8))
                    reg_date_china = reg_date.astimezone(china_tz)
                    
                    # 格式化注册时间显示 (使用中国时间)
                    formatted_time = reg_date_china.strftime("%Y-%m-%d %H:%M:%S")
                    self.register_time_label.setText(f"注册时间: {formatted_time}")
                    
                    # 去除时区信息以便比较
                    reg_date = reg_date.replace(tzinfo=None)
                    
                    # 计算总试用期限（14天）
                    trial_period = timedelta(days=14)
                    expiry_date = reg_date + trial_period
                    
                    # 计算已经使用的时间
                    used_time = now - reg_date
                    used_days = used_time.days
                    remaining_days_float = 14 - used_days - (used_time.seconds / 86400)  # 精确到小数
                    
                    # 如果已过期或剩余不足1天
                    if now > expiry_date or remaining_days_float < 1:
                        remaining_days = "已过期"
                        self.remaining_days_label.setSpecialText(remaining_days)
                        self.remaining_days_label.setColor(Theme.ERROR)
                    else:
                        # 计算剩余天数（总试用期14天 - 已使用天数）
                        remaining_days = 14 - used_days
                        self.remaining_days_label.setValue(remaining_days, animate=True)
                        self.remaining_days_label.setColor(Theme.SUCCESS)
                    
                    # 将注册时间和剩余天数作为当前邮箱的临时信息
                    # 不再需要更新账户数据，因为我们直接从数据库获取token并实时查询
                except Exception as e:
                    print(f"计算剩余天数时出错: {str(e)}")
                    self.register_time_label.setText("--")
                    self.remaining_days_label.setSpecialText("未知")
                    self.remaining_days_label.setColor(Theme.TEXT_SECONDARY)
            else:
                # 没有真实注册时间，显示默认值
                self.register_time_label.setText("注册时间: --")
                self.remaining_days_label.setValue(0, animate=False)
                self.remaining_days_label.setColor(Theme.TEXT_SECONDARY)
            
            # 高级模型 (GPT-4)
            gpt4_data = quota_data.get("gpt-4", {})
            usage = gpt4_data.get("numRequestsTotal", 0)
            max_usage = gpt4_data.get("maxRequestUsage", 150)
            
            # 检查高级模型使用上限，当≥500时显示"尊贵的有钱人Pro用户"
            if max_usage >= 500:
                # 临时保存原始前缀并设置为空
                original_prefix = self.remaining_days_label.prefix
                self.remaining_days_label.prefix = ""
                self.remaining_days_label.setSpecialText("尊贵的有钱人Pro用户")
                self.remaining_days_label.setColor(Theme.GOLD)  # 使用金色突出显示
            
            if max_usage is not None:
                if max_usage > 0:
                    self.advanced_progress.setRange(0, max_usage)
                    self.advanced_progress.setValue(usage)  # 显示已使用量
                    self.advanced_progress.setFormat(f"{usage}/{max_usage}")
                    
                    # 根据使用情况设置不同颜色
                    usage_percent = usage / max_usage
                    if usage_percent > 0.9:
                        # 已用超过90%，显示红色警告
                        self.advanced_progress.setChunkColor(Theme.ERROR)
                    elif usage_percent > 0.7:
                        # 已用超过70%，显示黄色警告
                        self.advanced_progress.setChunkColor(Theme.WARNING)
                    else:
                        # 默认绿色
                        self.advanced_progress.setChunkColor(Theme.ACCENT)
                else:
                    self.advanced_progress.setRange(0, 100)
                    self.advanced_progress.setValue(100)
                    self.advanced_progress.setFormat("无限制")
            else:
                self.advanced_progress.setRange(0, 100)
                self.advanced_progress.setValue(0)
                self.advanced_progress.setFormat("未知")
            
            # 普通模型 (GPT-3.5)
            gpt35_data = quota_data.get("gpt-3.5-turbo", {})
            usage = gpt35_data.get("numRequestsTotal", 0)
            max_usage = gpt35_data.get("maxRequestUsage")
            
            if max_usage is not None:
                if max_usage > 0:
                    self.regular_progress.setRange(0, max_usage)
                    self.regular_progress.setValue(usage)  # 显示已使用量
                    self.regular_progress.setFormat(f"{usage}/{max_usage}")
                    
                    # 根据使用情况设置不同颜色
                    usage_percent = usage / max_usage
                    if usage_percent > 0.9:
                        # 已用超过90%，显示红色警告
                        self.regular_progress.setChunkColor(Theme.ERROR)
                    elif usage_percent > 0.7:
                        # 已用超过70%，显示黄色警告
                        self.regular_progress.setChunkColor(Theme.WARNING)
                    else:
                        # 默认绿色
                        self.regular_progress.setChunkColor(Theme.ACCENT)
                else:
                    self.regular_progress.setRange(0, 100)
                    self.regular_progress.setValue(100)
                    self.regular_progress.setFormat("无限制")
            else:
                self.regular_progress.setRange(0, 100)
                self.regular_progress.setValue(100)
                self.regular_progress.setFormat("无限制")
        except Exception as e:
            print(f"更新使用额度UI时出错: {str(e)}")
            self.advanced_progress.setFormat("获取失败")
            self.regular_progress.setFormat("获取失败")
            self.register_time_label.setText("注册时间: --")
            self.remaining_days_label.setSpecialText("未知")
            self.remaining_days_label.setColor(Theme.TEXT_SECONDARY)
    
    def _show_fetch_error(self):
        """显示获取错误信息"""
        self.advanced_progress.setFormat("获取失败")
        self.regular_progress.setFormat("获取失败")
        self.register_time_label.setText("注册时间: --")
        self.remaining_days_label.setSpecialText("获取失败")
        self.remaining_days_label.setColor(Theme.ERROR)
        self.show_toast("获取数据失败，请检查网络连接或重启应用", error=True)
    
    def _update_accounts_count(self):
        """更新账户计数信息"""
        # 获取账户总数
        total_accounts = len(self.account_data.accounts)
        
        # 计算过期账户数
        expired_accounts = 0
        for account in self.account_data.accounts:
            remaining_days = account.get("real_remaining_days", "")
            if remaining_days == "已过期" or remaining_days == "试用Pro已过期":
                expired_accounts += 1
                
        # 计算有效账户数
        active_accounts = total_accounts - expired_accounts
        
        # 更新显示
        self.accounts_count_label.setText(f"共 {total_accounts} 个账户（{active_accounts} 个有效，{expired_accounts} 个过期）")


# Toast消息队列管理类
class ToastQueue(QObject):
    """Toast消息队列管理器，确保消息按顺序显示"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent = parent
        self.queue = deque()
        self.current_toast = None
        self.is_showing = False
        self.max_queue_size = 10  # 最大队列长度
    
    def add_message(self, text, duration=2000, error=False):
        """添加新消息到队列
        
        Args:
            text: 消息文本
            duration: 显示持续时间(毫秒)
            error: 是否是错误消息
        """
        if len(self.queue) >= self.max_queue_size:
            # 队列已满，移除最早的消息
            self.queue.popleft()
        
        # 添加新消息到队列
        self.queue.append({
            'text': text,
            'duration': duration,
            'error': error
        })
        
        # 如果当前没有显示消息，开始显示
        if not self.is_showing:
            self._show_next()
    
    def _show_next(self):
        """显示队列中的下一条消息"""
        if not self.queue:
            self.is_showing = False
            return
        
        # 获取下一条消息
        message = self.queue.popleft()
        
        # 创建并显示Toast
        self.is_showing = True
        self.current_toast = ToastMessage(
            self.parent,
            message['text'],
            message['duration'],
            Theme.ERROR if message['error'] else None
        )
        
        # 连接动画完成信号
        self.current_toast.animation_finished.connect(self._on_toast_finished)
        
        # 显示Toast
        self.current_toast.showToast()
    
    def _on_toast_finished(self):
        """当前Toast显示完成的回调"""
        # 清理当前Toast
        if self.current_toast:
            self.current_toast.deleteLater()
            self.current_toast = None
        
        # 显示下一条消息
        self._show_next()

# Toast消息提示类
class ToastMessage(QLabel):
    """Toast消息提示，短暂显示后自动消失"""
    
    # 定义动画完成信号
    animation_finished = pyqtSignal()
    
    def __init__(self, parent, text, duration=2000, color=None):
        super().__init__(parent)
        self.parent = parent
        self.setText(text)
        self.duration = duration
        self.animation_group = None
        
        # 设置样式 - 使用醒目的实心背景
        if color == Theme.ERROR:
            bg_color = Theme.ERROR
            border_color = "#D64550"  # 稍亮的红色边框
        else:
            bg_color = Theme.ACCENT
            border_color = Theme.ACCENT_HOVER
        
        # 设置整体样式
        self.setStyleSheet(f"""
            background-color: {bg_color};
            color: white;
            border: 1px solid {border_color};
            border-radius: {Theme.BORDER_RADIUS_SMALL};
            padding: 12px 20px;
            font-size: {Theme.FONT_SIZE_NORMAL};
            font-weight: bold;
        """)
        
        # 设置位置
        self.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.setWordWrap(False)  # 禁用自动换行
        
        # 添加阴影效果
        shadow = QGraphicsDropShadowEffect(self)
        shadow.setBlurRadius(20)
        shadow.setColor(QColor(0, 0, 0, 100))
        shadow.setOffset(0, 5)
        self.setGraphicsEffect(shadow)
        
        # 初始化隐藏
        self.hide()
        
    def hideEvent(self, event):
        """重写隐藏事件，确保停止动画"""
        # 确保动画被停止
        if self.animation_group and self.animation_group.state() == QAbstractAnimation.State.Running:
            self.animation_group.stop()
        super().hideEvent(event)
        
    def showToast(self):
        """显示Toast消息"""
        # 如果有正在运行的动画，先停止
        if self.animation_group and self.animation_group.state() == QAbstractAnimation.State.Running:
            self.animation_group.stop()
            self.animation_group = None
        
        # 调整大小以适应内容
        self.adjustSize()
        
        # 确保宽度足够
        min_width = 220  # 最小宽度
        if self.width() < min_width:
            self.setFixedWidth(min_width)
        
        # 设置位置在底部中央
        parent_rect = self.parent.rect()
        x = (parent_rect.width() - self.width()) // 2
        y = parent_rect.height() - self.height() - 40  # 距底部距离
        
        # 设置初始位置（从下方开始）
        self.move(x, parent_rect.height() + 20)
        self.show()
        
        # 创建位置动画（滑入）
        position_in = QPropertyAnimation(self, b"pos")
        position_in.setDuration(200)
        position_in.setStartValue(QPoint(x, parent_rect.height() + 20))
        position_in.setEndValue(QPoint(x, y))
        position_in.setEasingCurve(QEasingCurve.Type.OutCubic)
        
        # 创建位置动画（滑出）
        position_out = QPropertyAnimation(self, b"pos")
        position_out.setDuration(200)
        position_out.setStartValue(QPoint(x, y))
        position_out.setEndValue(QPoint(x, parent_rect.height() + 20))
        position_out.setEasingCurve(QEasingCurve.Type.InCubic)
        
        # 创建动画组
        self.animation_group = QSequentialAnimationGroup()
        self.animation_group.addAnimation(position_in)
        self.animation_group.addPause(self.duration)
        self.animation_group.addAnimation(position_out)
        
        # 连接信号，确保在动画完成后隐藏并发送完成信号
        self.animation_group.finished.connect(self._on_animation_finished)
        
        # 开始动画
        self.animation_group.start()
    
    def _on_animation_finished(self):
        """动画完成后的处理"""
        # 隐藏Toast
        self.hide()
        # 删除动画组
        if self.animation_group:
            self.animation_group.deleteLater()
            self.animation_group = None
        # 发送完成信号
        self.animation_finished.emit()


class StyledDialog(QDialog):
    """样式化对话框，无标题栏，带模糊背景效果"""
    
    def __init__(self, parent=None, title=None, width=400):
        super().__init__(parent)
        
        # 无边框窗口设置
        self.setWindowFlags(Qt.WindowType.FramelessWindowHint | Qt.WindowType.Dialog)
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)
        
        # 设置模态
        self.setModal(True)
        
        # 设置固定宽度
        self.setFixedWidth(width)
        
        # 保存父窗口引用
        self.parent_widget = parent
        
        # 背景遮罩
        self.overlay = None
        self.create_overlay()
        
        # 创建主布局
        self.main_layout = QVBoxLayout(self)
        self.main_layout.setContentsMargins(0, 0, 0, 0)
        self.main_layout.setSpacing(0)
        
        # 创建背景框架
        self.bg_frame = QFrame(self)
        self.bg_frame.setObjectName("bg_frame")
        self.bg_frame.setStyleSheet(f"""
            #bg_frame {{
                background-color: #121317;
                border: 1px solid {Theme.GLASS_BORDER};
                border-radius: {Theme.BORDER_RADIUS};
            }}
        """)
        
        # 背景框架布局
        self.bg_layout = QVBoxLayout(self.bg_frame)
        self.bg_layout.setContentsMargins(0, 0, 0, 25)
        self.bg_layout.setSpacing(0)
        
        # 添加标题栏（如果有标题）
        if title:
            # 创建标题区域
            title_area = QFrame()
            title_area.setObjectName("titleArea")
            title_area.setFixedHeight(50)  # 固定高度
            title_area.setStyleSheet(f"""
                #titleArea {{
                    background-color: #121317;
                    border-top-left-radius: {Theme.BORDER_RADIUS};
                    border-top-right-radius: {Theme.BORDER_RADIUS};
                }}
            """)
            
            # 标题区域布局
            title_layout = QHBoxLayout(title_area)
            title_layout.setContentsMargins(20, 0, 20, 0)
            
            # 标题文本
            title_label = QLabel(title)
            title_label.setStyleSheet(f"""
                color: {Theme.TEXT_PRIMARY};
                font-weight: bold;
                font-size: {Theme.FONT_SIZE_TITLE};
            """)
            title_layout.addWidget(title_label)
            
            # 添加弹性空间
            title_layout.addStretch(1)
            
            # 关闭按钮
            close_btn = QPushButton("×")
            close_btn.setCursor(Qt.CursorShape.PointingHandCursor)
            close_btn.setFixedSize(30, 30)
            close_btn.setStyleSheet(f"""
                QPushButton {{
                    background-color: transparent;
                    color: {Theme.TEXT_SECONDARY};
                    font-size: 20px;
                    border: none;
                    border-radius: 15px;
                }}
                QPushButton:hover {{
                    background-color: {Theme.WINDOW_CLOSE};
                    color: white;
                }}
            """)
            close_btn.clicked.connect(self.reject)
            title_layout.addWidget(close_btn)
            
            # 将标题区域添加到布局中
            self.bg_layout.addWidget(title_area)
            
            # 创建内容容器来恢复边距
            content_container = QWidget()
            content_container_layout = QVBoxLayout(content_container)
            content_container_layout.setContentsMargins(25, 20, 25, 0)
            content_container_layout.setSpacing(20)
            
            # 将内容容器添加到背景布局
            self.bg_layout.addWidget(content_container)
            
            # 使用内容容器的布局代替背景布局
            self.content_parent_layout = content_container_layout
        else:
            # 如果没有标题，调整背景布局的内边距
            self.bg_layout.setContentsMargins(25, 25, 25, 25)
            self.bg_layout.setSpacing(20)
            self.content_parent_layout = self.bg_layout
        
        # 将背景框架添加到主布局
        self.main_layout.addWidget(self.bg_frame)
        
        # 初始化拖动相关变量
        self.drag_position = None
    
    def addWidget(self, widget):
        """添加控件到内容区域"""
        self.content_parent_layout.addWidget(widget)
        return widget
        
    def addLayout(self, layout):
        """添加布局到内容区域"""
        self.content_parent_layout.addLayout(layout)
        return layout
    
    def addButtons(self, confirm_text="确认", cancel_text="取消", confirm_color=None):
        """添加按钮到对话框底部"""
        button_layout = QHBoxLayout()
        button_layout.setSpacing(10)
        
        # 使用主题色或自定义颜色
        if confirm_color is None:
            confirm_color = Theme.ACCENT
        
        # 确认按钮
        self.confirm_btn = QPushButton(confirm_text)
        self.confirm_btn.setCursor(Qt.CursorShape.PointingHandCursor)
        self.confirm_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {confirm_color};
                color: white;
                border: none;
                border-radius: {Theme.BORDER_RADIUS_SMALL};
                padding: 10px 20px;
                font-weight: bold;
                min-width: 80px;
            }}
            QPushButton:hover {{
                background-color: {Theme.ACCENT_HOVER};
            }}
            QPushButton:pressed {{
                background-color: {confirm_color};
                opacity: 0.8;
            }}
        """)
        
        # 如果有取消按钮文本，添加取消按钮
        if cancel_text:
            # 取消按钮
            self.cancel_btn = QPushButton(cancel_text)
            self.cancel_btn.setCursor(Qt.CursorShape.PointingHandCursor)
            self.cancel_btn.setStyleSheet(f"""
                QPushButton {{
                    background-color: #2A2E36;
                    color: {Theme.TEXT_PRIMARY};
                    border: 1px solid {Theme.BORDER};
                    border-radius: {Theme.BORDER_RADIUS_SMALL};
                    padding: 10px 20px;
                    font-weight: bold;
                    min-width: 80px;
                }}
                QPushButton:hover {{
                    border: 1px solid {Theme.ACCENT};
                    color: {Theme.ACCENT};
                }}
                QPushButton:pressed {{
                    background-color: #252830;
                }}
            """)
            self.cancel_btn.clicked.connect(self.reject)
            
            # 添加按钮到布局
            button_layout.addWidget(self.confirm_btn)
            button_layout.addWidget(self.cancel_btn)
        else:
            # 只有确认按钮时居中显示
            button_layout.addStretch()
            button_layout.addWidget(self.confirm_btn)
            button_layout.addStretch()
        
        # 添加按钮布局到内容区域
        self.content_parent_layout.addLayout(button_layout)
        
        return self.confirm_btn
    
    def mousePressEvent(self, event):
        """鼠标按下事件，用于实现窗口拖动"""
        if event.button() == Qt.MouseButton.LeftButton:
            self.drag_position = event.globalPosition().toPoint() - self.frameGeometry().topLeft()
            event.accept()
    
    def mouseMoveEvent(self, event):
        """鼠标移动事件，用于实现窗口拖动"""
        if event.buttons() == Qt.MouseButton.LeftButton and self.drag_position:
            self.move(event.globalPosition().toPoint() - self.drag_position)
            event.accept()
    
    def mouseReleaseEvent(self, event):
        """鼠标释放事件"""
        if event.button() == Qt.MouseButton.LeftButton:
            self.drag_position = None
            event.accept()
    
    @staticmethod
    def showConfirmDialog(parent, title, text, confirm_text="确认", cancel_text="取消", confirm_color=None):
        """显示确认对话框，返回是否确认"""
        dialog = StyledDialog(parent, title)
        
        # 创建消息文本的背景框架
        message_frame = QFrame()
        message_frame.setObjectName("messageFrame")
        message_frame.setStyleSheet(f"""
            #messageFrame {{
                background-color: #121317;
                border-radius: {Theme.BORDER_RADIUS_SMALL};
            }}
        """)
        
        message_frame_layout = QVBoxLayout(message_frame)
        message_frame_layout.setContentsMargins(20, 20, 20, 20)
        
        # 消息文本
        message_label = QLabel(text)
        message_label.setWordWrap(True)
        message_label.setStyleSheet(f"""
            color: {Theme.TEXT_PRIMARY};
            font-size: {Theme.FONT_SIZE_NORMAL};
            background-color: transparent;
            padding: 0px;
        """)
        message_frame_layout.addWidget(message_label)
        
        # 添加消息框架到对话框
        dialog.addWidget(message_frame)
        
        confirm_btn = dialog.addButtons(confirm_text, cancel_text, confirm_color)
        confirm_btn.clicked.connect(dialog.accept)
        
        return dialog.exec() == QDialog.DialogCode.Accepted
    
    @staticmethod
    def showInfoDialog(parent, title, text, ok_text="确定"):
        """显示信息对话框"""
        dialog = StyledDialog(parent, title)
        
        # 创建消息文本的背景框架
        message_frame = QFrame()
        message_frame.setObjectName("messageFrame")
        message_frame.setStyleSheet(f"""
            #messageFrame {{
                background-color: #121317;
                border-radius: {Theme.BORDER_RADIUS_SMALL};
            }}
        """)
        
        message_frame_layout = QVBoxLayout(message_frame)
        message_frame_layout.setContentsMargins(20, 20, 20, 20)
        
        # 消息文本
        message_label = QLabel(text)
        message_label.setWordWrap(True)
        message_label.setStyleSheet(f"""
            color: {Theme.TEXT_PRIMARY};
            font-size: {Theme.FONT_SIZE_NORMAL};
            background-color: transparent;
            padding: 0px;
        """)
        message_frame_layout.addWidget(message_label)
        
        # 添加消息框架到对话框
        dialog.addWidget(message_frame)
        
        # 添加确定按钮
        ok_btn = dialog.addButtons(ok_text)
        ok_btn.clicked.connect(dialog.accept)
        
        dialog.exec()
    
    def create_overlay(self):
        """创建半透明背景遮罩"""
        if self.parent_widget:
            # 创建遮罩控件
            self.overlay = QWidget(self.parent_widget)
            self.overlay.setObjectName("dialogOverlay")
            self.overlay.setStyleSheet("""
                #dialogOverlay {
                    background-color: rgba(0, 0, 0, 0.7);
                }
            """)
            # 设置遮罩大小覆盖整个父窗口
            self.overlay.setGeometry(self.parent_widget.rect())
            # 设置层级为父窗口的所有子控件最前
            self.overlay.stackUnder(self)
            # 确保遮罩在对话框下方但在其他控件上方
            self.overlay.raise_()
            self.overlay.hide()
            
            # 创建透明度效果，用于淡入淡出动画
            self.overlay_opacity = QGraphicsOpacityEffect(self.overlay)
            self.overlay_opacity.setOpacity(0.0)  # 初始透明度为0
            self.overlay.setGraphicsEffect(self.overlay_opacity)
            
            # 创建透明度动画
            self.overlay_anim = QPropertyAnimation(self.overlay_opacity, b"opacity")
            self.overlay_anim.setDuration(300)  # 300毫秒动画
            self.overlay_anim.setStartValue(0.0)
            self.overlay_anim.setEndValue(1.0)
            self.overlay_anim.setEasingCurve(QEasingCurve.Type.OutCubic)
            
            # 设置遮罩的事件过滤，防止点击穿透
            self.overlay.mousePressEvent = lambda e: e.accept()
    
    def showEvent(self, event):
        """对话框显示时显示背景遮罩"""
        super().showEvent(event)
        
        # 显示背景遮罩并播放淡入动画
        if self.overlay:
            # 更新遮罩尺寸以匹配父窗口
            self.overlay.setGeometry(self.parent_widget.rect())
            self.overlay.show()
            
            # 启动淡入动画
            self.overlay_anim.setDirection(QAbstractAnimation.Direction.Forward)
            self.overlay_anim.start()
            
            # 将对话框提到前面
            self.raise_()
    
    def hideEvent(self, event):
        """对话框隐藏时隐藏背景遮罩"""
        # 停止任何正在进行的动画并隐藏遮罩
        if self.overlay:
            self.overlay_anim.stop()
            self.overlay.hide()
        
        super().hideEvent(event)
    
    def exec(self):
        """重写exec方法，确保遮罩处理"""
        # 显示遮罩并播放淡入动画
        if self.overlay:
            # 确保遮罩大小与父窗口匹配
            self.overlay.setGeometry(self.parent_widget.rect())
            self.overlay.show()
            
            # 启动淡入动画
            self.overlay_anim.setDirection(QAbstractAnimation.Direction.Forward)
            self.overlay_anim.start()
            
            # 确保对话框在遮罩上层
            self.raise_()
            
        # 先处理一下所有未处理的事件，确保对话框大小已计算
        QApplication.processEvents()
        
        # 强制布局计算以确保大小正确
        self.updateGeometry()
        self.adjustSize()
        
        # 居中显示对话框
        if self.parent_widget:
            # 获取父窗口的屏幕位置和中心点
            parent_geometry = self.parent_widget.frameGeometry()
            parent_center = parent_geometry.center()
            
            # 获取对话框的几何信息
            dialog_frame = self.frameGeometry()
            
            # 将对话框中心点设置到父窗口中心点
            dialog_frame.moveCenter(parent_center)
            dest_pos = dialog_frame.topLeft()
            
            # 确保对话框在屏幕内
            screen = QApplication.primaryScreen()
            screen_geometry = screen.availableGeometry()
            
            # 如果对话框超出屏幕边界，调整位置
            if dest_pos.x() < screen_geometry.left():
                dest_pos.setX(screen_geometry.left() + 10)
            elif dest_pos.x() + dialog_frame.width() > screen_geometry.right():
                dest_pos.setX(screen_geometry.right() - dialog_frame.width() - 10)
                
            if dest_pos.y() < screen_geometry.top():
                dest_pos.setY(screen_geometry.top() + 10)
            elif dest_pos.y() + dialog_frame.height() > screen_geometry.bottom():
                dest_pos.setY(screen_geometry.bottom() - dialog_frame.height() - 10)
                
            self.move(dest_pos)
        
        result = super().exec()
        
        # 隐藏遮罩（动画已在hideEvent中处理）
        if self.overlay:
            self.overlay_anim.stop()
            self.overlay.hide()
        
        return result


# 主应用窗口
class CursorAccountManager(QMainWindow):
    """Cursor账户管理器主窗口类"""
    
    def __init__(self):
        """初始化主窗口"""
        super().__init__()
        
        # 初始化数据管理器
        self.auth_manager = CursorAuthManager()
        self.account_data = AccountData()
        
        # 清理旧的临时文件
        self._cleanup_old_temp_files()
        
        # 当前账户信息
        self.current_email = self.auth_manager.get_current_email()
        self.account_rows = {}  # 邮箱 -> 行组件的映射
        self.account_quotas = {}  # 存储所有账户的额度数据，邮箱 -> 额度数据
        self.toast_queue = ToastQueue(self)  # 创建消息队列管理器
        self.entrance_anim = None  # 入场动画
        
        # 添加页面状态跟踪变量
        self.is_first_accounts_page_load = True  # 标记是否是首次加载账户管理页面
        
        # 设置窗口属性
        self.setWindowTitle("Cursor账户管理器")
        self.setMinimumSize(1100, 780)  # 设置最小尺寸
        
        # 去掉默认窗口边框
        self.setWindowFlags(Qt.WindowType.FramelessWindowHint)
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)
        
        # 初始化UI
        self.init_ui()
        
        # 注意：不在构造函数中加载数据
        # 数据加载会在入场动画结束后进行
        
        # 创建自动刷新定时器 - 每30秒自动刷新一次当前账户额度
        self.quota_refresh_timer = QTimer(self)
        self.quota_refresh_timer.timeout.connect(self._auto_refresh_current_quota)
        self.quota_refresh_timer.setInterval(6 * 1000)  # 30秒
    
    def _cleanup_old_temp_files(self):
        """清理旧的临时文件"""
        try:
            # 获取应用程序路径
            if getattr(sys, 'frozen', False):
                application_path = os.path.dirname(sys.executable)
            else:
                application_path = os.path.dirname(os.path.abspath(__file__))
            
            # 查找所有以temp_开头的JSON文件
            for file in os.listdir(application_path):
                if file.startswith("temp_") and file.endswith(".json"):
                    try:
                        temp_file_path = os.path.join(application_path, file)
                        # 检查是否是当前使用的临时文件
                        if temp_file_path != self.account_data.temp_file:
                            # 删除过期的临时文件
                            os.remove(temp_file_path)
                            print(f"已删除旧的临时文件: {file}")
                    except Exception as e:
                        print(f"删除临时文件出错: {str(e)}")
        except Exception as e:
            print(f"清理临时文件时出错: {str(e)}")
    
    def kill_cursor_process(self):
        """
        杀死Cursor进程
        返回：是否成功杀死Cursor进程的布尔值
        """
        try:
            cursor_killed = False
            
            # Windows系统
            if sys.platform == "win32":
                # 使用tasklist查找进程，仅匹配确切的进程名Cursor.exe
                find_cmd = 'tasklist /FI "IMAGENAME eq Cursor.exe" /FO CSV /NH'
                result = subprocess.run(find_cmd, shell=True, capture_output=True, text=True)
                
                if "Cursor.exe" in result.stdout:
                    # 使用taskkill杀死进程
                    kill_cmd = 'taskkill /F /IM Cursor.exe'
                    subprocess.run(kill_cmd, shell=True, capture_output=True)
                    cursor_killed = True
                    print("Windows: 已结束Cursor进程")
            
            # macOS系统
            elif sys.platform == "darwin":
                # 查找进程ID，确保只找 /Applications/Cursor.app 的进程
                find_cmd = 'pgrep -f "^/Applications/Cursor.app/Contents/MacOS/Cursor"'
                result = subprocess.run(find_cmd, shell=True, capture_output=True, text=True)
                
                if result.stdout.strip():
                    # 杀死进程
                    kill_cmd = f'kill -9 {result.stdout.strip()}'
                    subprocess.run(kill_cmd, shell=True)
                    cursor_killed = True
                    print("macOS: 已结束Cursor进程")
            
            # Linux系统
            elif sys.platform == "linux":
                # 查找进程ID，使用更精确的匹配
                find_cmd = 'pgrep -f "^/usr/bin/cursor|^/usr/local/bin/cursor|^/opt/cursor/cursor"'
                result = subprocess.run(find_cmd, shell=True, capture_output=True, text=True)
                
                if result.stdout.strip():
                    # 杀死进程
                    kill_cmd = f'kill -9 {result.stdout.strip()}'
                    subprocess.run(kill_cmd, shell=True)
                    cursor_killed = True
                    print("Linux: 已结束Cursor进程")
            
            return cursor_killed
        except Exception as e:
            print(f"结束Cursor进程时出错: {str(e)}")
            return False
            
    def start_cursor_app(self):
        """
        启动Cursor应用程序，确保其作为独立进程运行且不显示终端窗口
        """
        try:
            # Windows系统
            if sys.platform == "win32":
                cursor_path = os.path.join(os.getenv("LOCALAPPDATA"), "Programs", "cursor", "Cursor.exe")
                if os.path.exists(cursor_path):
                    # 使用Windows内置的start命令启动程序，不显示命令提示符
                    # /B 参数表示不创建新的控制台窗口
                    # start命令会创建一个完全独立的进程
                    subprocess.run('start /B "" "' + cursor_path + '"', shell=True)
                    print("Windows: 已启动Cursor应用")
                    return True
            
            # macOS系统
            elif sys.platform == "darwin":
                # 使用open命令启动，-n参数强制创建新实例
                # -g参数使应用在后台启动而不获取焦点
                subprocess.run(["open", "-n", "-g", "-a", "Cursor"], shell=False)
                print("macOS: 已启动Cursor应用")
                return True
            
            # Linux系统
            elif sys.platform == "linux":
                # 尝试多个可能的路径
                for cursor_path in ["/usr/bin/cursor", "/usr/local/bin/cursor", "/opt/cursor/cursor"]:
                    if os.path.exists(cursor_path):
                        # 使用nohup启动，并在后台运行，禁止输出
                        subprocess.run(
                            f"nohup {cursor_path} >/dev/null 2>&1 & disown",
                            shell=True,
                            start_new_session=True
                        )
                        print("Linux: 已启动Cursor应用")
                        return True
            
            # 如果没有找到可执行文件
            self.show_toast("无法找到Cursor应用程序", error=True)
            return False
        except Exception as e:
            print(f"启动Cursor应用程序时出错: {str(e)}")
            self.show_toast(f"启动Cursor应用程序时出错: {str(e)}", error=True)
            return False
    
    def reload_progress_animations(self):
        """重新加载所有进度条动画，根据页面类型执行不同逻辑"""
        # 获取当前页面
        current_widget = self.content_stack.currentWidget()
        if not current_widget:
            return
        
        # 检查是否刚刚执行过动画恢复，避免短时间内重复执行
        current_time = time.time()
        if hasattr(self, '_last_animation_reload_time'):
            # 如果两次恢复间隔小于300ms，跳过
            if current_time - self._last_animation_reload_time < 0.3:
                print(f"动画恢复频率过高，跳过本次恢复")
                return
        
        # 更新最后动画恢复时间
        self._last_animation_reload_time = current_time
        
        # 检查当前是否在账户管理页面
        if current_widget == self.accounts_page:
            # 如果是首次加载账户管理页面，显示加载状态
            if self.is_first_accounts_page_load:
                # 设置所有账户行为加载状态
                for row in self.account_rows.values():
                    row.set_loading_state(True)
                
                self.is_first_accounts_page_load = False
                print("首次加载账户管理页面，设置加载状态")
                
                # 立即进行数据加载，但将动画恢复安排在数据加载后
                self.fetch_all_accounts_quota(is_manual_refresh=True, show_toast=False)
                # 动画恢复会在_restore_animations方法中延迟执行
            # 移除自动刷新功能 - 不在非首次加载时自动刷新数据
            # else:
            #     # 非首次加载，才加载账户数据
            #     self.fetch_all_accounts_quota(is_manual_refresh=True, show_toast=False)
            #     print("非首次账户管理页面加载，已刷新数据")
        elif current_widget == self.home_page:
            # 如果切换到了首页，先尝试从存储加载数据
            if self.advanced_progress.format() == "加载中...":
                print("首页进度条显示加载中，尝试从存储加载数据")
                # 先尝试从存储加载
                if not self._load_current_account_quota_from_storage():
                    # 如果从存储加载失败，才请求API
                    print("从存储加载失败，请求API获取数据")
                    QTimer.singleShot(0, lambda: self._fetch_current_account_quota())
        
        # 不管是哪个页面，都安排延迟恢复动画，确保数据加载有时间完成
        QTimer.singleShot(50, lambda: self._restore_animations(current_widget))
    
    def _restore_animations(self, current_widget):
        """在页面切换完成后恢复所有动画"""
        try:
            # 查找当前页面中的所有进度条
            progress_bars = current_widget.findChildren(StyledProgressBar)
            
            # 等待数据加载的时间，确保API数据已返回
            animation_delay_base = 50  # 基础延迟
            
            # 对每个进度条重新应用值以触发动画，使用错开的延迟时间
            for i, progress_bar in enumerate(progress_bars):
                try:
                    # 计算每个进度条的递增延迟，避免所有动画同时启动
                    pb_delay = animation_delay_base + (i * 50)  # 每个增加50ms
                    
                    # 使用延迟触发恢复，确保数据先加载完成
                    QTimer.singleShot(pb_delay, lambda bar=progress_bar: self._delayed_restore_bar(bar))
                except Exception as e:
                    print(f"设置进度条动画延迟恢复时出错: {str(e)}")
            
            # 查找当前页面中的所有数字动画标签
            animated_labels = current_widget.findChildren(AnimatedNumberLabel)
            
            # 对每个数字标签重置并重新启动动画，使用更长的延迟
            for i, label in enumerate(animated_labels):
                try:
                    # 添加递增延迟，避免所有动画同时启动造成卡顿
                    label_delay = animation_delay_base + 100 + (i * 50)  # 比进度条多100ms基础延迟
                    QTimer.singleShot(label_delay, lambda lbl=label: self._delayed_restore_label(lbl))
                except Exception as e:
                    print(f"设置数字标签动画延迟恢复时出错: {str(e)}")
        except Exception as e:
            print(f"恢复动画时出错: {str(e)}")
    
    def _delayed_restore_bar(self, progress_bar, force=False):
        """延迟恢复进度条动画，确保数据已加载
        
        Args:
            progress_bar: 要恢复的进度条
            force: 是否强制恢复，即使短时间内已恢复过，默认False
        """
        try:
            # 检查进度条是否已经正在运行动画，如果是则不重复触发
            if not force and hasattr(progress_bar, '_animation') and progress_bar._animation.state() == QAbstractAnimation.State.Running:
                return
            
            # 记录上一次恢复时间，避免短时间内多次恢复
            current_time = time.time()
            if not force and hasattr(progress_bar, '_last_restore_time'):
                # 如果两次恢复间隔小于1秒，则跳过
                if current_time - progress_bar._last_restore_time < 1.0:
                    return
            
            # 更新最后恢复时间
            progress_bar._last_restore_time = current_time
            
            # 恢复进度条的原始值并触发动画
            progress_bar.restore_original_value()
            
            # 如果是AnimatedProgressBar，确保数字动画也正确恢复
            if isinstance(progress_bar, AnimatedProgressBar):
                # 延迟一小段时间后恢复数字动画，避免同时触发导致的卡顿
                QTimer.singleShot(50, progress_bar.reset_animation)
        except Exception as e:
            print(f"延迟恢复进度条动画时出错: {str(e)}")
    
    def _delayed_restore_label(self, label, force=False):
        """延迟恢复数字标签动画，确保数据已加载
        
        Args:
            label: 要恢复的数字标签
            force: 是否强制恢复，即使短时间内已恢复过，默认False
        """
        try:
            # 记录上一次恢复时间，避免短时间内多次恢复
            current_time = time.time()
            if not force and hasattr(label, '_last_restore_time'):
                # 如果两次恢复间隔小于1秒，则跳过
                if current_time - label._last_restore_time < 1.0:
                    return
            
            # 更新最后恢复时间
            label._last_restore_time = current_time
            
            # 重置并启动数字标签动画
            label.reset_animation()
        except Exception as e:
            print(f"延迟恢复数字标签动画时出错: {str(e)}")
    
    def init_ui(self):
        """初始化用户界面"""
        # 创建主窗口背景
        self.main_bg = QWidget(self)
        self.main_bg.setObjectName("mainBackground")
        self.main_bg.setStyleSheet(f"""
            QWidget#mainBackground {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                                          stop:0 {Theme.GRADIENT_START}, 
                                          stop:1 {Theme.GRADIENT_END});
                border-radius: {Theme.BORDER_RADIUS};
                border: 1px solid {Theme.BORDER};
            }}
        """)
        
        # 初始时将背景设为半透明，用于淡入动画
        self.main_bg_effect = QGraphicsOpacityEffect(self.main_bg)
        self.main_bg_effect.setOpacity(0.5)
        self.main_bg.setGraphicsEffect(self.main_bg_effect)
        
        # 创建主布局
        self.main_layout = QVBoxLayout(self.main_bg)
        self.main_layout.setContentsMargins(0, 0, 0, 0)
        self.main_layout.setSpacing(0)
        
        # 添加自定义标题栏
        self.title_bar = CustomTitleBar(self)
        self.main_layout.addWidget(self.title_bar)
        
        # 创建内容区域
        self.content_widget = QWidget()
        self.content_widget.setObjectName("contentWidget")
        self.content_widget.setStyleSheet("""
            QWidget#contentWidget {
                background: transparent;
            }
        """)
        
        self.content_layout = QHBoxLayout(self.content_widget)
        self.content_layout.setContentsMargins(20, 10, 20, 20)
        self.content_layout.setSpacing(20)
        
        # 创建左侧菜单
        self.sidebar = self.create_sidebar()
        self.content_layout.addWidget(self.sidebar)
        
        # 创建内容区域
        self.content_stack = AnimatedStackedWidget()
        self.content_stack.setObjectName("contentStack")
        self.content_stack.setStyleSheet("""
            QStackedWidget#contentStack {
                background: transparent;
            }
        """)
        
        # 设置动画参数
        self.content_stack.setDirection(Qt.Orientation.Horizontal)  # 水平方向滑动
        self.content_stack.setSpeed(350)  # 动画持续时间，毫秒
        self.content_stack.setAnimation(QEasingCurve.Type.OutCubic)  # 动画曲线类型
        
        # 创建主页
        self.home_page = self.create_home_page()
        self.content_stack.addWidget(self.home_page)
        
        # 创建账户管理页面
        self.accounts_page = self.create_accounts_page()
        self.content_stack.addWidget(self.accounts_page)
        
        self.content_layout.addWidget(self.content_stack)
        
        # 添加内容区域到主布局
        self.main_layout.addWidget(self.content_widget)
        
        # 连接页面切换动画完成信号，在切换后重新加载进度条动画
        self.content_stack.animationFinished.connect(self.reload_progress_animations)
        
        # 设置主窗口布局
        self.setCentralWidget(self.main_bg)
    
    def create_sidebar(self):
        """创建左侧菜单栏"""
        # 创建侧边栏容器
        sidebar = StyledFrame(has_glass_effect=True)
        sidebar.setFixedWidth(240)
        sidebar.setObjectName("sidebar")
        
        # 应用玻璃效果样式到侧边栏
        sidebar.setStyleSheet(f"""
            #sidebar {{
                background-color: {Theme.GLASS_BG};
                border-radius: {Theme.BORDER_RADIUS};
                border: 1px solid {Theme.GLASS_BORDER};
            }}
        """)
        
        # 创建侧边栏布局
        layout = QVBoxLayout(sidebar)
        layout.setContentsMargins(10, 25, 10, 20)
        layout.setSpacing(15)
        
        # 添加标题
        title_layout = QHBoxLayout()
        
        title_label = QLabel("YCursor")
        title_label.setStyleSheet(f"""
            font-size: {Theme.FONT_SIZE_TITLE};
            font-weight: bold;
            color: {Theme.ACCENT};
            padding: 0px 10px 0px 10px;
            background-color: transparent;
        """)
        
        title_layout.addWidget(title_label)
        title_layout.addStretch()
        
        layout.addLayout(title_layout)
        layout.addSpacing(30)
        
        # 创建首页按钮
        self.home_btn = QPushButton("  首页")
        self.home_btn.setIcon(QIcon("icons/home.png"))
        self.home_btn.setCheckable(True)
        self.home_btn.setChecked(True)
        self.home_btn.setCursor(Qt.CursorShape.PointingHandCursor)
        self.home_btn.clicked.connect(lambda: self.switch_page(0))
        
        # 创建账号管理按钮
        self.accounts_btn = QPushButton("  账号管理")
        self.accounts_btn.setIcon(QIcon("icons/accounts.png"))
        self.accounts_btn.setCheckable(True)
        self.accounts_btn.setCursor(Qt.CursorShape.PointingHandCursor)
        self.accounts_btn.clicked.connect(lambda: self.switch_page(1))
        
        # 设置按钮样式
        button_style = f"""
            QPushButton {{
                text-align: left;
                padding: 12px 20px;
                font-size: 16px;
                border: none;
                background-color: transparent;
                color: {Theme.TEXT_SECONDARY};
                border-radius: {Theme.BORDER_RADIUS_SMALL};
            }}
            QPushButton:hover {{
                background-color: {Theme.SECONDARY};
                color: {Theme.TEXT_PRIMARY};
            }}
            QPushButton:checked {{
                background-color: {Theme.ACCENT};
                color: {Theme.TEXT_PRIMARY};
                font-weight: bold;
            }}
        """
        
        # 为首页按钮添加特殊样式，确保无下划线
        self.home_btn.setStyleSheet(button_style + """
            QPushButton {
                border-bottom: none !important;
                text-decoration: none !important;
                outline: none !important;
            }
            QPushButton:checked {
                border-bottom: none !important;
                text-decoration: none !important;
                outline: none !important;
            }
            QPushButton:focus {
                border-bottom: none !important;
                text-decoration: none !important;
                outline: none !important;
            }
        """)
        
        # 为账号管理按钮使用基础样式
        self.accounts_btn.setStyleSheet(button_style)
        
        # 添加按钮到布局
        layout.addWidget(self.home_btn)
        layout.addWidget(self.accounts_btn)
        layout.addStretch()
        
        # 添加底部信息
        footer_frame = QFrame()
        footer_frame.setStyleSheet(f"""
            QFrame {{
                background-color: {Theme.CARD_LEVEL_2};
                border-radius: {Theme.BORDER_RADIUS_SMALL};
                padding: 10px;
            }}
        """)
        
        footer_layout = QVBoxLayout(footer_frame)
        footer_layout.setContentsMargins(15, 15, 15, 15)
        
        # 版本信息
        current_version = Utils.get_cursor_version()
        version_label = QLabel(f"Cursor版本: {current_version}")
        version_label.setStyleSheet(f"color: {Theme.TEXT_SECONDARY}; font-size: {Theme.FONT_SIZE_SMALL};")
        
        # 获取YCursor版本
        from version_checker import VersionChecker
        checker = VersionChecker()
        app_version = checker.current_version
        app_version_label = QLabel(f"YCursor版本: {app_version}")
        app_version_label.setStyleSheet(f"color: {Theme.TEXT_SECONDARY}; font-size: {Theme.FONT_SIZE_SMALL};")
        
        footer_layout.addWidget(version_label)
        footer_layout.addWidget(app_version_label)
        
        layout.addWidget(footer_frame)
        
        return sidebar
    
    def create_home_page(self):
        """创建主页"""
        page = QWidget()
        page.setStyleSheet("background: transparent;")  # 设置整个页面背景为透明
        layout = QVBoxLayout(page)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(25)
        
        # 添加当前账户信息卡片
        account_frame = StyledFrame(has_glass_effect=True)
        account_layout = QVBoxLayout(account_frame)
        account_layout.setContentsMargins(25, 25, 25, 25)
        
        # 修改为与模型使用情况区域一致的背景色
        account_frame.setStyleSheet(f"""
            StyledFrame {{
                background-color: {Theme.GLASS_BG};
                border-radius: {Theme.BORDER_RADIUS};
                border: 1px solid {Theme.GLASS_BORDER};
            }}
        """)
        
        # 当前登录账户标题改为透明背景
        account_title = QLabel("当前登录账户")
        account_title.setStyleSheet(f"""
            font-size: {Theme.FONT_SIZE_TITLE}; 
            font-weight: bold; 
            color: {Theme.ACCENT}; 
            margin-bottom: 20px;
            background-color: transparent;
            padding: 8px 15px;
            border-radius: {Theme.BORDER_RADIUS_SMALL};
        """)
        account_layout.addWidget(account_title)
        
        # 邮箱和版本信息
        info_frame = QFrame()
        info_frame.setStyleSheet(f"""
            QFrame {{
                background-color: {Theme.CARD_LEVEL_2};
                border-radius: {Theme.BORDER_RADIUS_SMALL};
                padding: 15px;
            }}
        """)
        
        info_layout = QVBoxLayout(info_frame)
        info_layout.setSpacing(15)
        
        self.email_label = QLabel("邮箱: 加载中...")
        self.email_label.setStyleSheet(f"""
            font-size: {Theme.FONT_SIZE_NORMAL}; 
            padding: 8px 12px;
            background-color: transparent;
            border-radius: {Theme.BORDER_RADIUS_SMALL};
        """)
        
        # 添加注册时间
        self.register_time_label = QLabel("注册时间: 加载中...")
        self.register_time_label.setStyleSheet(f"""
            font-size: {Theme.FONT_SIZE_NORMAL}; 
            padding: 8px 12px;
            background-color: transparent;
            border-radius: {Theme.BORDER_RADIUS_SMALL};
        """)
        
        # 添加剩余时间 - 使用动画标签
        self.remaining_days_label = AnimatedNumberLabel(prefix="剩余时间:", suffix="天", duration=2000)
        self.remaining_days_label.setSpecialText("加载中...")
        self.remaining_days_label.setColor(Theme.SUCCESS)
        # 不再需要单独设置样式，因为已在类中设置了与QLabel一致的样式
        
        info_layout.addWidget(self.email_label)
        info_layout.addWidget(self.register_time_label)  # 添加注册时间
        info_layout.addWidget(self.remaining_days_label)  # 添加剩余时间
        
        account_layout.addWidget(info_frame)
        
        layout.addWidget(account_frame)
        
        # 添加使用情况卡片
        usage_frame = StyledFrame(has_glass_effect=True)
        usage_layout = QVBoxLayout(usage_frame)
        usage_layout.setContentsMargins(25, 25, 25, 25)
        
        usage_title = QLabel("模型使用情况")
        usage_title.setStyleSheet(f"""
            font-size: {Theme.FONT_SIZE_TITLE}; 
            font-weight: bold; 
            color: {Theme.ACCENT}; 
            margin-bottom: 20px;
            background-color: transparent;
        """)
        usage_layout.addWidget(usage_title)
        
        # 模型使用情况容器
        models_frame = QFrame()
        models_frame.setStyleSheet(f"""
            QFrame {{
                background-color: {Theme.CARD_LEVEL_2};
                border-radius: {Theme.BORDER_RADIUS_SMALL};
                padding: 20px;
            }}
        """)
        
        models_layout = QVBoxLayout(models_frame)
        models_layout.setSpacing(25)
        
        # 高级模型使用情况
        advanced_model_frame = QFrame()
        advanced_model_frame.setStyleSheet(f"""
            QFrame {{
                background-color: transparent;
                border-radius: {Theme.BORDER_RADIUS_SMALL};
                padding: 15px;
            }}
        """)
        
        advanced_model_layout = QVBoxLayout(advanced_model_frame)
        advanced_model_layout.setContentsMargins(0, 0, 0, 0)  # 移除内边距使标签左对齐
        advanced_model_layout.setSpacing(10)
        
        # 修改高级模型标签
        advanced_model_label = QLabel("高级模型使用量")
        advanced_model_label.setStyleSheet(f"""
            font-size: {Theme.FONT_SIZE_NORMAL}; 
            font-weight: bold;
            color: {Theme.TEXT_PRIMARY};
            background-color: transparent;
            padding-left: 0px;  /* 移除左边距使文字左对齐 */
        """)
        
        # 使用支持数字动画的进度条
        self.advanced_progress = AnimatedProgressBar()
        self.advanced_progress.setMinimumHeight(25)
        self.advanced_progress.setRange(0, 100)
        self.advanced_progress.setValue(0)
        self.advanced_progress.setFormat("加载中...")
        
        advanced_model_layout.addWidget(advanced_model_label)
        advanced_model_layout.addWidget(self.advanced_progress)
        
        # 普通模型使用情况
        regular_model_frame = QFrame()
        regular_model_frame.setStyleSheet(f"""
            QFrame {{
                background-color: transparent;
                border-radius: {Theme.BORDER_RADIUS_SMALL};
                padding: 15px;
            }}
        """)
        
        regular_model_layout = QVBoxLayout(regular_model_frame)
        regular_model_layout.setContentsMargins(0, 0, 0, 0)  # 移除内边距使标签左对齐
        regular_model_layout.setSpacing(10)
        
        # 修改普通模型标签
        regular_model_label = QLabel("普通模型使用量")
        regular_model_label.setStyleSheet(f"""
            font-size: {Theme.FONT_SIZE_NORMAL}; 
            font-weight: bold;
            color: {Theme.TEXT_PRIMARY};
            background-color: transparent;
            padding-left: 0px;  /* 移除左边距使文字左对齐 */
        """)
        
        # 使用支持数字动画的进度条
        self.regular_progress = AnimatedProgressBar()
        self.regular_progress.setMinimumHeight(25)
        self.regular_progress.setRange(0, 100)
        self.regular_progress.setValue(0)
        self.regular_progress.setFormat("加载中...")
        
        regular_model_layout.addWidget(regular_model_label)
        regular_model_layout.addWidget(self.regular_progress)
        
        # 添加到模型布局
        models_layout.addWidget(advanced_model_frame)
        models_layout.addWidget(regular_model_frame)
        
        usage_layout.addWidget(models_frame)
        
        layout.addWidget(usage_frame)
        
        layout.addStretch()
        
        return page
    
    def create_accounts_page(self):
        """创建账户管理页面"""
        accounts_page = QWidget()
        
        # 创建主布局
        main_layout = QVBoxLayout(accounts_page)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(15)
        
        # 标题行
        title_layout = QHBoxLayout()
        title_layout.setContentsMargins(0, 0, 0, 0)
        title_layout.setSpacing(20)
        
        # 账户总数统计
        self.accounts_count_label = QLabel("加载中...")
        self.accounts_count_label.setStyleSheet(f"""
            color: {Theme.TEXT_SECONDARY};
            font-size: {Theme.FONT_SIZE_NORMAL};
            margin-bottom: 10px;
        """)
        title_layout.addWidget(self.accounts_count_label)
        title_layout.addStretch()
        
        # 添加批量删除和刷新按钮（按钮顺序调整为：删除已过期->按额度删除->刷新状态）
        delete_all_expired_btn = QPushButton("删除已过期")
        delete_all_expired_btn.setCursor(Qt.CursorShape.PointingHandCursor)
        delete_all_expired_btn.setStyleSheet(f"""
            QPushButton {{
                color: {Theme.TEXT_PRIMARY};
                background-color: {Theme.CARD_LEVEL_2};
                border: none;
                border-radius: {Theme.BORDER_RADIUS_SMALL};
                padding: 8px 15px;
                font-size: {Theme.FONT_SIZE_NORMAL};
            }}
            QPushButton:hover {{
                background-color: {Theme.ERROR};
                color: white;
            }}
            QPushButton:pressed {{
                background-color: #A8414E;
                color: white;
            }}
        """)
        delete_all_expired_btn.clicked.connect(self.delete_expired_accounts)
        title_layout.addWidget(delete_all_expired_btn)
        
        delete_by_quota_btn = QPushButton("按额度删除")
        delete_by_quota_btn.setCursor(Qt.CursorShape.PointingHandCursor)
        delete_by_quota_btn.setStyleSheet(f"""
            QPushButton {{
                color: {Theme.TEXT_PRIMARY};
                background-color: {Theme.CARD_LEVEL_2};
                border: none;
                border-radius: {Theme.BORDER_RADIUS_SMALL};
                padding: 8px 15px;
                font-size: {Theme.FONT_SIZE_NORMAL};
            }}
            QPushButton:hover {{
                background-color: {Theme.WARNING};
                color: white;
            }}
            QPushButton:pressed {{
                background-color: #B89C5D;
                color: white;
            }}
        """)
        delete_by_quota_btn.clicked.connect(self.show_delete_quota_dialog)
        title_layout.addWidget(delete_by_quota_btn)
        
        # 创建刷新和删除按钮
        refresh_btn = QPushButton("刷新状态")
        refresh_btn.setCursor(Qt.CursorShape.PointingHandCursor)
        refresh_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {Theme.ACCENT};
                color: {Theme.TEXT_PRIMARY};
                border: none;
                border-radius: {Theme.BORDER_RADIUS_SMALL};
                padding: 8px 15px;
                font-size: {Theme.FONT_SIZE_NORMAL};
            }}
            QPushButton:hover {{
                background-color: {Theme.ACCENT_HOVER};
            }}
            QPushButton:pressed {{
                background-color: #249070;
            }}
        """)
        
        # 连接刷新按钮到获取所有账户额度的方法
        refresh_btn.clicked.connect(lambda: self.fetch_all_accounts_quota(is_manual_refresh=True))
        title_layout.addWidget(refresh_btn)
        
        main_layout.addLayout(title_layout)
        
        # 添加分隔线
        line = QFrame()
        line.setFrameShape(QFrame.Shape.HLine)
        line.setFrameShadow(QFrame.Shadow.Sunken)
        line.setStyleSheet(f"""
            color: {Theme.BORDER};
            background-color: {Theme.BORDER};
        """)
        main_layout.addWidget(line)
        
        # 创建列表标题
        header_frame = QFrame()
        header_frame.setStyleSheet(f"""
            background-color: {Theme.CARD_LEVEL_2};
                border-radius: {Theme.BORDER_RADIUS_SMALL};
                padding: 0px;
            margin: 4px 2px;
        """)
        
        header_layout = QHBoxLayout(header_frame)
        header_layout.setContentsMargins(15, 12, 15, 12)
        header_layout.setSpacing(5)  # 减少组件间的间距，与行组件一致
        
        # 左侧 - 账户信息区域
        left_header = QWidget()
        left_header.setStyleSheet("background: transparent;")
        left_layout = QVBoxLayout(left_header)
        left_layout.setContentsMargins(0, 0, 0, 0)
        left_layout.setSpacing(6)
        
        email_header = QLabel("账户信息")
        email_header.setStyleSheet(f"""
            color: {Theme.TEXT_PRIMARY}; 
            font-weight: bold;
            font-size: {Theme.FONT_SIZE_NORMAL};
        """)
        left_layout.addWidget(email_header)
        
        # 中间部分 - 包含剩余天数和进度条
        center_header = QWidget()
        center_header.setStyleSheet("background: transparent;")
        center_layout = QVBoxLayout(center_header)
        center_layout.setContentsMargins(0, 0, 0, 0)
        center_layout.setSpacing(6)
        
        status_header = QLabel("账户状态")
        status_header.setStyleSheet(f"""
            color: {Theme.TEXT_PRIMARY}; 
                font-weight: bold; 
            font-size: {Theme.FONT_SIZE_NORMAL};
        """)
        center_layout.addWidget(status_header)
        
        # 右侧 - 操作按钮
        right_header = QWidget()
        right_header.setStyleSheet("background: transparent;")
        right_layout = QHBoxLayout(right_header)
        right_layout.setContentsMargins(5, 0, 0, 0)  # 减少左侧边距为5像素
        right_layout.setSpacing(5)  # 减少间距
        right_layout.setAlignment(Qt.AlignmentFlag.AlignLeft | Qt.AlignmentFlag.AlignVCenter)  # 左对齐
        
        actions_header = QLabel("操作")
        actions_header.setStyleSheet(f"""
            color: {Theme.TEXT_PRIMARY}; 
            font-weight: bold;
            font-size: {Theme.FONT_SIZE_NORMAL};
            padding-left: 0px;
            text-align: left;
        """)
        actions_header.setAlignment(Qt.AlignmentFlag.AlignLeft)  # 设置文本左对齐
        right_layout.addWidget(actions_header)
        
        # 添加到主布局，使用与行组件相同的比例
        header_layout.addWidget(left_header, 3)
        header_layout.addWidget(center_header, 3)
        header_layout.addWidget(right_header, 2)
        
        main_layout.addWidget(header_frame)
        
        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setStyleSheet(f"""
            QScrollArea {{
                border: none;
                background-color: transparent;
            }}
            QScrollBar:vertical {{
                border: none;
                background-color: {Theme.CARD_LEVEL_1};
                width: 10px;
                margin: 0px;
            }}
            QScrollBar::handle:vertical {{
                background-color: {Theme.CARD_LEVEL_3};
                border-radius: 5px;
                min-height: 20px;
            }}
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {{
                height: 0px;
            }}
            QScrollBar::add-page:vertical, QScrollBar::sub-page:vertical {{
                background: none;
            }}
        """)
        
        # 创建账户列表区域
        self.accounts_container = QWidget()
        self.accounts_container.setStyleSheet(f"""
            background-color: transparent;
            border: none;
        """)
        self.accounts_layout = QVBoxLayout(self.accounts_container)
        self.accounts_layout.setContentsMargins(0, 0, 0, 0)
        self.accounts_layout.setSpacing(10)
        self.accounts_layout.setAlignment(Qt.AlignmentFlag.AlignTop)
        
        # 不再添加Loading占位文本
        # loading_label = QLabel("加载账户信息中...")
        # loading_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        # loading_label.setStyleSheet(f"""
        #     color: {Theme.TEXT_SECONDARY};
        #     font-size: {Theme.FONT_SIZE_NORMAL};
        #     padding: 20px;
        # """)
        # self.accounts_layout.addWidget(loading_label)
        
        scroll_area.setWidget(self.accounts_container)
        main_layout.addWidget(scroll_area, 1)
        
        return accounts_page
    
    def show_delete_quota_dialog(self):
        """显示删除额度账户的对话框"""
        dialog = StyledDialog(self, "按使用次数删除账户")
        
        # 添加说明文本
        description = QLabel("按照高级模型的已使用次数进行筛选删除：")
        description.setStyleSheet(f"""
                color: {Theme.TEXT_PRIMARY};
                font-size: {Theme.FONT_SIZE_NORMAL};
        """)
        dialog.addWidget(description)
        
        # 创建条件选择区域的背景框架
        conditions_frame = QFrame()
        conditions_frame.setObjectName("conditionsFrame")
        conditions_frame.setStyleSheet(f"""
            #conditionsFrame {{
                background-color: #121317;
                border-radius: {Theme.BORDER_RADIUS_SMALL};
            }}
        """)
        
        frame_layout = QVBoxLayout(conditions_frame)
        frame_layout.setContentsMargins(15, 15, 15, 15)
        
        # 创建条件选择区域
        condition_layout = QHBoxLayout()
        condition_layout.setSpacing(10)
        
        # 操作符选择
        self.operator_combo = QComboBox()
        self.operator_combo.addItems(["大于等于", "小于等于", "等于"])
        self.operator_combo.setStyleSheet(f"""
            QComboBox {{
                background-color: #121317;
                color: {Theme.TEXT_PRIMARY};
                border: 1px solid {Theme.BORDER};
                border-radius: {Theme.BORDER_RADIUS_SMALL};
                padding: 8px 15px 8px 15px;
                min-width: 120px;
                font-size: {Theme.FONT_SIZE_NORMAL};
            }}
            QComboBox:hover {{
                border: 1px solid {Theme.ACCENT};
                background-color: #181b21;
            }}
            QComboBox:focus {{
                border: 1px solid {Theme.ACCENT};
                background-color: #181b21;
            }}
            QComboBox::drop-down {{
                subcontrol-origin: padding;
                subcontrol-position: right center;
                width: 20px;
                border-left: none;
                padding-right: 5px;
            }}
            QComboBox::down-arrow {{
                width: 10px;
                height: 10px;
                image: none;
                border-top: 5px solid {Theme.TEXT_PRIMARY};
                border-right: 5px solid transparent;
                border-left: 5px solid transparent;
            }}
            QComboBox QAbstractItemView {{
                background-color: #121317;
                color: {Theme.TEXT_PRIMARY};
                border: 1px solid {Theme.BORDER};
                border-radius: {Theme.BORDER_RADIUS_SMALL};
                selection-background-color: {Theme.ACCENT};
                outline: none;
                padding: 5px;
            }}
            QComboBox QAbstractItemView::item {{
                padding: 8px 10px;
                min-height: 25px;
                border-radius: {Theme.BORDER_RADIUS_SMALL};
            }}
            QComboBox QAbstractItemView::item:hover {{
                background-color: #1E2128;
            }}
            QComboBox QAbstractItemView::item:selected {{
                background-color: {Theme.ACCENT};
                color: white;
            }}
        """)
        condition_layout.addWidget(self.operator_combo)
        
        # 额度输入
        self.quota_input = QLineEdit()
        self.quota_input.setPlaceholderText("输入额度")
        self.quota_input.setStyleSheet(f"""
            QLineEdit {{
                background-color: #121317;
                color: {Theme.TEXT_PRIMARY};
                border: 1px solid {Theme.BORDER};
                border-radius: {Theme.BORDER_RADIUS_SMALL};
                padding: 8px 15px;
                min-width: 120px;
                font-size: {Theme.FONT_SIZE_NORMAL};
            }}
            QLineEdit:hover {{
                border: 1px solid {Theme.ACCENT};
                background-color: #181b21;
            }}
            QLineEdit:focus {{
                border: 1px solid {Theme.ACCENT};
                background-color: #181b21;
            }}
        """)
        condition_layout.addWidget(self.quota_input)
        
        # 添加条件布局到框架
        frame_layout.addLayout(condition_layout)
        
        # 添加框架到对话框
        dialog.addWidget(conditions_frame)
        
        # 添加确认和取消按钮
        confirm_btn = dialog.addButtons("确认", "取消")
        # 将确认按钮连接到处理函数
        confirm_btn.clicked.connect(lambda: self.delete_accounts_by_quota(dialog))
        
        dialog.exec()

    def delete_accounts_by_quota(self, dialog):
        """删除指定额度的账户"""
        # 获取输入的额度
        quota_text = self.quota_input.text().strip()
        
        if not quota_text:
            self.show_toast("请输入要删除的账户额度", error=True)
            return
        
        try:
            quota = int(quota_text)
        except:
            self.show_toast("请输入有效的额度数值", error=True)
            return
        
        # 获取操作符
        operator = self.operator_combo.currentText()
        
        # 确认是否要删除
        if not Utils.confirm_message(
            self,
            "删除指定额度账户",
            f"确定要删除所有高级模型使用次数{operator}{quota}的账户吗？",
        ):
            return
        
        # 在执行删除前，检查是否所有账户都已获取额度信息
        accounts_without_quota = [acc for acc in self.account_data.accounts if 'real_usage' not in acc]
        if accounts_without_quota:
            message = f"有{len(accounts_without_quota)}个账户未获取额度信息，是否先刷新所有账户额度？"
            if Utils.confirm_message(self, "建议先刷新额度", message):
                # 显示正在获取额度的提示
                self.show_toast("正在获取账户额度，请稍候...")
                # 获取所有账户额度
                self.fetch_all_accounts_quota(show_toast=True)
                # 在获取完成后执行删除操作
                self.quotaFetcher.all_quotas_fetched.connect(lambda: self._execute_delete_by_quota(quota, operator, dialog))
                return  # 先返回，等待额度获取完成
            
        # 直接执行删除
        self._execute_delete_by_quota(quota, operator, dialog)
    
    def _execute_delete_by_quota(self, quota, operator, dialog):
        """实际执行按额度删除的操作"""
        # 如果是从信号槽调用，断开连接避免重复执行
        try:
            self.quotaFetcher.all_quotas_fetched.disconnect()
        except:
            pass
        
        # 执行删除，获取被删除的账户邮箱列表
        deleted_emails = self.account_data.delete_accounts_by_quota(quota, operator)
        
        if deleted_emails:
            self.show_toast(f"已删除 {len(deleted_emails)} 个高级模型使用次数{operator}{quota}的账户")
            
            # 1. 重新加载本地存储的账户数据
            self.account_data.load_accounts()
            
            # 2. 清除已删除账户的行
            for email in deleted_emails:
                if email in self.account_rows:
                    row_widget = self.account_rows[email]
                    self.accounts_layout.removeWidget(row_widget)
                    row_widget.deleteLater()
                    del self.account_rows[email]
            
            # 3. 更新账户计数
            self._update_accounts_count()
            
            # 4. 更新当前账户UI（如果当前账户被删除）
            if self.current_email in deleted_emails:
                # 如果还有账户，切换到第一个
                if self.account_data.accounts:
                    new_current = self.account_data.accounts[0]
                    self.current_email = new_current.get("email", "")
                else:
                    # 没有账户了，清空当前邮箱
                    self.current_email = ""
                self.load_current_account()
            
            # 5. 仅使用不重建UI的排序方法
            self._sort_accounts_without_rebuild_ui()
            
            # 6. 只在有账户的情况下刷新额度数据
            if self.account_data.accounts:
                # 这里使用手动刷新模式，避免重建UI
                self.fetch_all_accounts_quota(is_manual_refresh=True, show_toast=False)
        else:
            self.show_toast(f"没有发现高级模型使用次数{operator}{quota}的账户")
        
        dialog.accept()
    
    def switch_page(self, index):
        """切换页面"""
        if self.content_stack.currentIndex() == index:
            return
            
        # 更新按钮状态
        self.home_btn.setChecked(index == 0)
        self.accounts_btn.setChecked(index == 1)
        
        # 获取目标页面，并预处理其进度条
        target_widget = self.content_stack.widget(index)
        if target_widget:
            # 查找目标页面中的所有进度条
            progress_bars = target_widget.findChildren(StyledProgressBar)
            
            # 将所有进度条值设为0，预先隐藏已填充状态
            for progress_bar in progress_bars:
                try:
                    # 使用专用方法重置进度条，同时存储原始值
                    progress_bar.reset_without_animation(store_original=True)
                except Exception as e:
                    print(f"预处理进度条时出错: {str(e)}")
        
        # 使用动画效果切换页面
        self.content_stack.slideInIdx(index)
    
    def run_entrance_animation(self):
        """运行入场动画"""
        # 获取窗口原始尺寸和位置
        screen_geometry = QApplication.primaryScreen().availableGeometry()
        window_geometry = self.geometry()
        
        # 设置初始位置和尺寸（从中心点开始）
        center_x = screen_geometry.width() // 2
        center_y = screen_geometry.height() // 2
        initial_width = window_geometry.width() // 2
        initial_height = window_geometry.height() // 2
        
        initial_geometry = QRect(
            center_x - initial_width // 2,
            center_y - initial_height // 2,
            initial_width,
            initial_height
        )
        
        # 设置目标位置和尺寸
        target_geometry = window_geometry
        
        # 动画时长和曲线优化
        animation_duration = 300  # 更短的动画时间，避免卡顿感
        
        # 阻止窗口更新以避免动画中重绘引起的闪烁
        self.setUpdatesEnabled(False)
        
        # 创建窗口几何动画
        geo_anim = QPropertyAnimation(self, b"geometry")
        geo_anim.setDuration(animation_duration)
        geo_anim.setStartValue(initial_geometry)
        geo_anim.setEndValue(target_geometry)
        geo_anim.setEasingCurve(QEasingCurve.Type.OutQuint)  # 使用更平滑的曲线
        
        # 创建窗口不透明度动画
        opacity_anim = QPropertyAnimation(self, b"windowOpacity")
        opacity_anim.setDuration(animation_duration)
        opacity_anim.setStartValue(0.2)  # 从0.2开始，避免完全透明
        opacity_anim.setEndValue(1.0)
        opacity_anim.setEasingCurve(QEasingCurve.Type.OutQuint)
        
        # 创建背景淡入动画
        bg_opacity_anim = QPropertyAnimation(self.main_bg_effect, b"opacity")
        bg_opacity_anim.setDuration(animation_duration + 100)  # 稍长一点，让背景淡入更平滑
        bg_opacity_anim.setStartValue(0.5)
        bg_opacity_anim.setEndValue(1.0)
        bg_opacity_anim.setEasingCurve(QEasingCurve.Type.OutCubic)
        
        # 创建动画组
        self.entrance_anim = QParallelAnimationGroup()
        self.entrance_anim.addAnimation(geo_anim)
        self.entrance_anim.addAnimation(opacity_anim)
        self.entrance_anim.addAnimation(bg_opacity_anim)
        
        # 连接动画完成信号，在动画结束后执行其他操作
        self.entrance_anim.finished.connect(self._on_entrance_animation_finished)
        
        # 延迟一点点再启动动画，确保窗口已完全渲染
        QTimer.singleShot(20, self._start_entrance_animation)
    
    def _start_entrance_animation(self):
        """实际启动入场动画"""
        try:
            # 在动画前确保所有部件已完全创建和布局
            self.update()
            QApplication.processEvents()
            
            # 一次性设置所有属性，减少可能的闪烁
            self.setUpdatesEnabled(True)
            
            # 启动动画
            self.entrance_anim.start()
        except Exception as e:
            print(f"启动入场动画时出错: {str(e)}")
            # 出现错误时尝试恢复正常状态
            self.setWindowOpacity(1.0)
            self.main_bg.setGraphicsEffect(None)
            self.setUpdatesEnabled(True)
    
    def _on_entrance_animation_finished(self):
        """入场动画完成后的回调"""
        # 清除所有特效，确保后续渲染正常
        self.main_bg.setGraphicsEffect(None)
        
        # 在动画完成后加载数据，避免动画过程中的干扰
        self.load_data()
        
        # 设置账户管理页面初始加载标志为true
        # 确保第一次点击账户管理标签时数据是加载状态
        self.is_first_accounts_page_load = True
        
        # 启动定时刷新的定时器
        self.quota_refresh_timer.start()
        print("自动刷新: 定时器已启动，每30秒自动刷新一次当前账户额度")
    
    def load_data(self):
        """加载所有数据"""
        try:
            # 仅加载最基础数据，避免动画卡顿
            self.load_current_account()
            
            # 延迟加载首页数据，确保入场动画完成
            QTimer.singleShot(500, self._delayed_data_loading)
        except Exception as e:
            print(f"加载数据时出错: {str(e)}")
    
    def _delayed_data_loading(self):
        """延迟加载数据，确保入场动画流畅"""
        try:
            # 分步加载数据，避免UI阻塞
            # 第一步：只加载账户列表UI，不获取额度数据
            QTimer.singleShot(0, self.load_accounts)
            
            # 第二步：只获取当前账户(首页)的配额信息，不获取其他账户
            # 不再调用获取所有账户额度的方法
            # QTimer.singleShot(600, lambda: self._fetch_other_accounts_quota())
        except Exception as e:
            print(f"延迟加载数据时出错: {str(e)}")
    
    def _fetch_other_accounts_quota(self):
        """获取除当前账户外的所有其他账户额度
        
        由于当前账户的额度已经被优先获取，这个方法只负责获取其他账户的额度数据
        """
        # 确保不重复获取当前账户的额度，只获取其他账户
        try:
            # 过滤出非当前账户的其他账户
            other_accounts = [
                account for account in self.account_data.accounts 
                if account.get("email") != self.current_email
            ]
            
            # 如果没有其他账户，则不需要进一步操作
            if not other_accounts:
                print("没有其他账户需要获取额度")
                return
            
            # 创建只针对其他账户的额度获取器
            fetcher = QuotaFetcher(other_accounts)
            
            # 连接信号
            fetcher.account_quota_updated.connect(self.update_account_quota)
            fetcher.all_quotas_fetched.connect(
                lambda: self.on_all_quotas_fetched(is_manual_refresh=False, show_toast=False)
            )
            
            # 开始获取
            fetcher.start_fetching()
            
            # 在账户管理页面，只为其他账户设置加载状态
            for email, row in self.account_rows.items():
                if email != self.current_email:
                    try:
                        row.set_loading_state(True)
                    except Exception as e:
                        print(f"设置行加载状态时出错: {str(e)}")
                        
        except Exception as e:
            print(f"获取其他账户额度时出错: {str(e)}")
    
    def load_current_account(self):
        """加载当前账号信息并立即获取其额度"""
        # 获取当前登录邮箱
        if self.current_email:
            self.email_label.setText(f"邮箱: {self.current_email}")
        else:
            self.email_label.setText("邮箱: 未登录")
        
        # 设置更明显的加载状态
        self.register_time_label.setText("注册时间: 加载中...")
        self.remaining_days_label.setSpecialText("加载中...")
        self.remaining_days_label.setColor(Theme.ACCENT)
        
        # 重置进度条状态并显示加载状态
        self.advanced_progress.reset_without_animation()
        self.advanced_progress.setFormat("加载中...")
        self.regular_progress.reset_without_animation()
        self.regular_progress.setFormat("加载中...")
        
        # 如果有当前账户，立即获取其额度数据
        if self.current_email:
            # 立即启动一个线程获取当前账户数据
            # 独立获取当前账户数据，优先更新首页
            QTimer.singleShot(0, lambda: self._fetch_current_account_quota())
    
    def switch_account(self, account):
        """切换到指定账户"""
        email = account.get("email")
        auth_info = account.get("auth_info", {})
        
        # 确认是否要切换
        if not Utils.confirm_message(
            self,
            "切换账户",
            f"确定要切换到账户 {email} 吗？\n\n将自动关闭并重启Cursor应用程序。",
        ):
            return
        
        # 获取Token信息
        access_token = auth_info.get("cursorAuth/accessToken")
        refresh_token = auth_info.get("cursorAuth/refreshToken", access_token)
        
        if not email or not access_token:
            self.show_toast("账户信息不完整，无法切换", error=True)
            return

        # ============== 添加恢复机器码功能开始 ==============
        # 检查账户数据是否包含机器码信息
        machine_info = account.get("machine_info", {})
        account_system_type = account.get("system_type", "")
        
        # 获取当前系统类型
        current_system_type = ""
        import platform
        system = platform.system().lower()
        if "windows" in system:
            current_system_type = "windows"
        elif "darwin" in system:
            current_system_type = "mac"
        else:
            current_system_type = "linux"
        
        # 只有当账户有机器码信息且系统类型匹配时才恢复
        if machine_info and account_system_type and account_system_type == current_system_type:
            self.show_toast(f"检测到匹配的机器码信息，正在恢复...", error=False)
            
            try:
                import os
                import json
                import shutil
                import uuid
                import subprocess
                import tempfile
                
                # 获取storage.json路径
                storage_path = ""
                if current_system_type == "windows":
                    storage_path = os.path.join(os.getenv("APPDATA"), "Cursor", "User", "globalStorage", "storage.json")
                elif current_system_type == "mac":
                    storage_path = os.path.expanduser("~/Library/Application Support/Cursor/User/globalStorage/storage.json")
                elif current_system_type == "linux":
                    storage_path = os.path.expanduser("~/.config/Cursor/User/globalStorage/storage.json")
                
                # 检查storage.json是否存在
                if os.path.exists(storage_path):
                    # 读取当前配置
                    with open(storage_path, 'r', encoding='utf-8') as f:
                        config = json.load(f)
                    
                    # 更新telemetry相关的机器码
                    if "telemetry.machineId" in machine_info and machine_info["telemetry.machineId"]:
                        config["telemetry.machineId"] = machine_info["telemetry.machineId"]
                        print(f"已恢复 telemetry.machineId: {machine_info['telemetry.machineId']}")
                    
                    if "telemetry.macMachineId" in machine_info and machine_info["telemetry.macMachineId"]:
                        config["telemetry.macMachineId"] = machine_info["telemetry.macMachineId"]
                        print(f"已恢复 telemetry.macMachineId: {machine_info['telemetry.macMachineId']}")
                    
                    if "telemetry.devDeviceId" in machine_info and machine_info["telemetry.devDeviceId"]:
                        config["telemetry.devDeviceId"] = machine_info["telemetry.devDeviceId"]
                        print(f"已恢复 telemetry.devDeviceId: {machine_info['telemetry.devDeviceId']}")
                    
                    if "telemetry.sqmId" in machine_info and machine_info["telemetry.sqmId"]:
                        config["telemetry.sqmId"] = machine_info["telemetry.sqmId"]
                        print(f"已恢复 telemetry.sqmId: {machine_info['telemetry.sqmId']}")
                    
                    # 创建临时文件
                    with tempfile.NamedTemporaryFile(mode='w', delete=False) as temp:
                        # 写入更新后的JSON
                        json.dump(config, temp, indent=4)
                        temp_name = temp.name
                    
                    # 获取文件当前权限
                    current_mode = os.stat(storage_path).st_mode
                    
                    # 设置文件为可写
                    os.chmod(storage_path, 0o644)
                    
                    # 复制临时文件到原始位置
                    shutil.copy2(temp_name, storage_path)
                    
                    # 删除临时文件
                    os.unlink(temp_name)
                    
                    # 恢复原始权限
                    os.chmod(storage_path, current_mode)
                    
                    print(f"已成功更新配置文件: {storage_path}")
                    
                    # 根据系统类型恢复系统特有的机器码
                    machine_code_restored = False  # 跟踪机器码恢复状态
                    
                    if current_system_type == "windows":
                        # 恢复Windows的MachineGuid
                        if "system.machineGuid" in machine_info and machine_info["system.machineGuid"]:
                            try:
                                import winreg
                                key = winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\Microsoft\Cryptography", 0, winreg.KEY_SET_VALUE)
                                winreg.SetValueEx(key, "MachineGuid", 0, winreg.REG_SZ, machine_info["system.machineGuid"])
                                winreg.CloseKey(key)
                                print(f"已恢复 Windows MachineGuid: {machine_info['system.machineGuid']}")
                                machine_code_restored = True
                            except Exception as e:
                                error_msg = str(e)
                                winreg_error = f"恢复 Windows MachineGuid 失败: {error_msg}"
                                print(winreg_error)
                                # 尝试使用reg.exe命令
                                try:
                                    result = subprocess.run(
                                        ['reg', 'add', 'HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Cryptography', '/v', 'MachineGuid', '/t', 'REG_SZ', '/d', machine_info["system.machineGuid"], '/f'],
                                        check=True,
                                        capture_output=True
                                    )
                                    if result.returncode == 0:
                                        print(f"已使用reg.exe恢复 Windows MachineGuid: {machine_info['system.machineGuid']}")
                                        machine_code_restored = True
                                    else:
                                        error_msg = result.stderr.decode() if result.stderr else "未知错误"
                                        reg_error = f"使用reg.exe恢复 Windows MachineGuid 失败: {error_msg}"
                                        print(reg_error)
                                        # 保存详细错误信息
                                        self._last_error_detail = f"{winreg_error}\n{reg_error}"
                                        self.show_toast("恢复机器码失败，请以管理员身份运行程序", error=True, copy_error=True)
                                except Exception as e:
                                    error_msg = str(e)
                                    reg_error = f"使用reg.exe恢复 Windows MachineGuid 失败: {error_msg}"
                                    print(reg_error)
                                    # 保存详细错误信息
                                    self._last_error_detail = f"{winreg_error}\n{reg_error}"
                                    self.show_toast("恢复机器码失败，请以管理员身份运行程序", error=True, copy_error=True)
                    
                    elif current_system_type == "linux":
                        # 恢复Linux的machine-id
                        if "system.machineId" in machine_info and machine_info["system.machineId"]:
                            try:
                                with open("/etc/machine-id", 'w') as f:
                                    f.write(machine_info["system.machineId"])
                                print(f"已恢复 Linux machine-id: {machine_info['system.machineId']}")
                                machine_code_restored = True
                            except Exception as e:
                                error_msg = str(e)
                                error_detail = f"恢复 Linux machine-id 失败: {error_msg}"
                                print(error_detail)
                                self._last_error_detail = error_detail
                                self.show_toast("恢复机器码失败，请以root身份运行程序", error=True, copy_error=True)
                        
                        # 恢复Linux的dbus machine-id
                        if "system.dbusId" in machine_info and machine_info["system.dbusId"]:
                            try:
                                if os.path.exists("/var/lib/dbus/machine-id"):
                                    with open("/var/lib/dbus/machine-id", 'w') as f:
                                        f.write(machine_info["system.dbusId"])
                                    print(f"已恢复 Linux dbus machine-id: {machine_info['system.dbusId']}")
                                    machine_code_restored = True
                            except Exception as e:
                                error_msg = str(e)
                                error_detail = f"恢复 Linux dbus machine-id 失败: {error_msg}"
                                print(error_detail)
                                self._last_error_detail = error_detail
                                self.show_toast("恢复机器码失败，请以root身份运行程序", error=True, copy_error=True)
                    
                    elif current_system_type == "mac":
                        # 恢复Mac的nvram SystemUUID
                        if "system.nvramSystemUUID" in machine_info and machine_info["system.nvramSystemUUID"]:
                            try:
                                result = subprocess.run(["nvram", f"SystemUUID={machine_info['system.nvramSystemUUID']}"], check=True, capture_output=True)
                                if result.returncode == 0:
                                    print(f"已恢复 Mac nvram SystemUUID: {machine_info['system.nvramSystemUUID']}")
                                    machine_code_restored = True
                                else:
                                    error_msg = result.stderr.decode() if result.stderr else "未知错误"
                                    print(f"恢复 Mac nvram SystemUUID 失败: {error_msg}")
                                    self.show_toast("恢复机器码失败，请以root身份运行程序", error=True, copy_error=True)
                            except Exception as e:
                                error_msg = str(e)
                                error_detail = f"恢复 Mac nvram SystemUUID 失败: {error_msg}"
                                print(error_detail)
                                self._last_error_detail = error_detail
                                self.show_toast("恢复机器码失败，请以root身份运行程序", error=True, copy_error=True)
                    
                    # 只在机器码恢复成功时显示成功提示
                    if machine_code_restored:
                        self.show_toast(f"机器码已成功恢复", error=False)
                    else:
                        error_msg = f"机器码恢复失败，请以管理员身份运行程序"
                        self.show_toast(error_msg, error=True, copy_error=True)
                else:
                    error_msg = f"配置文件不存在: {storage_path}"
                    print(error_msg)
                    self.show_toast("配置文件不存在", error=True, copy_error=True)
            
            except Exception as e:
                error_msg = str(e)
                error_info = f"恢复机器码时出错: {error_msg}"
                print(error_info)
                self.show_toast("恢复机器码时出错", error=True, copy_error=True)
        # ============== 添加恢复机器码功能结束 ==============
        
        # 尝试结束Cursor进程
        self.show_toast("正在切换账户，请稍候...")
        cursor_killed = self.kill_cursor_process()
        
        # 更新认证信息
        success = self.auth_manager.update_auth(
            email=email,
            access_token=access_token,
            refresh_token=refresh_token
        )
        
        if success:
            # 如果成功更新认证信息
            if cursor_killed:
                self.show_toast(f"已切换到账户 {email}，正在重启Cursor...")
                # 给数据库写入操作留出一点时间
                QTimer.singleShot(1500, lambda: self.start_cursor_app())
            else:
                self.show_toast(f"已切换到账户 {email}，请手动重启Cursor应用")
            
            # 更新当前账户信息
            self.current_email = email
            
            # 加载当前账户基本UI
            self.load_current_account()
            
            # 不再直接调用load_accounts()，避免重复创建账户行
            # self.load_accounts()
            
            # 自动刷新所有账户状态信息（模拟点击"刷新状态"按钮）
            # fetch_all_accounts_quota在is_manual_refresh=True模式下会自动加载账户列表
            QTimer.singleShot(2000, lambda: self.fetch_all_accounts_quota(is_manual_refresh=True, show_toast=True))
        else:
            self.show_toast("切换账户失败", error=True)
    
    def delete_account(self, email):
        """删除账户"""
        # 确认是否删除
        if not Utils.confirm_message(
            self,
            "删除账户",
            f"确定要删除账户 {email} 吗？\n\n此操作不可恢复。",
        ):
            return
        
        try:
            # 尝试删除账户
            success = self.account_data.delete_account(email)
            
            if success:
                self.show_toast(f"账户 {email} 已删除")
                
                # 如果删除的是当前账户，切换到第一个账户
                if email == self.current_email:
                    # 如果还有账户，切换到第一个
                    if self.account_data.accounts:
                        new_current = self.account_data.accounts[0]
                        # 直接更新当前邮箱，不再需要current_account
                        self.current_email = new_current.get("email", "")
                    else:
                        # 没有账户了，清空当前邮箱
                        self.current_email = ""
                
                # 删除账户后，不要多次触发数据刷新和UI更新
                # 避免完整的UI重建，而是采用更精细的更新方式
                
                # 1. 重新加载本地存储的账户数据
                self.account_data.load_accounts()
                
                # 2. 清除已删除账户的行
                if email in self.account_rows:
                    row_widget = self.account_rows[email]
                    self.accounts_layout.removeWidget(row_widget)
                    row_widget.deleteLater()
                    del self.account_rows[email]
                
                # 3. 更新账户计数
                self._update_accounts_count()
                
                # 4. 如果删除的是当前账户，则需要更新当前账户UI
                if email == self.current_email:
                    self.load_current_account()
                
                # 5. 仅使用不重建UI的排序方法
                self._sort_accounts_without_rebuild_ui()
                
                # 6. 只在有账户的情况下刷新额度数据
                if self.account_data.accounts:
                    # 这里使用手动刷新模式，避免重建UI
                    self.fetch_all_accounts_quota(is_manual_refresh=True, show_toast=False)
            else:
                self.show_toast(f"删除账户 {email} 失败", error=True)
        except Exception as e:
            print(f"删除账户时出错: {str(e)}")
            self.show_toast(f"删除账户时出错: {str(e)}", error=True)
    
    def delete_expired_accounts(self):
        """删除所有过期账户"""
        # 确认是否要删除
        if not Utils.confirm_message(
            self,
            "删除过期账户",
            "确定要删除所有过期账户吗？",
        ):
            return
        
        # 执行删除，获取被删除的账户邮箱列表
        deleted_emails = self.account_data.delete_expired_accounts()
        
        if deleted_emails:
            self.show_toast(f"已删除 {len(deleted_emails)} 个过期账户")
            
            # 1. 重新加载本地存储的账户数据
            self.account_data.load_accounts()
            
            # 2. 清除已删除账户的行
            for email in deleted_emails:
                if email in self.account_rows:
                    row_widget = self.account_rows[email]
                    self.accounts_layout.removeWidget(row_widget)
                    row_widget.deleteLater()
                    del self.account_rows[email]
            
            # 3. 更新账户计数
            self._update_accounts_count()
            
            # 4. 更新当前账户UI（如果当前账户被删除）
            if self.current_email in deleted_emails:
                # 如果还有账户，切换到第一个
                if self.account_data.accounts:
                    new_current = self.account_data.accounts[0]
                    self.current_email = new_current.get("email", "")
                else:
                    # 没有账户了，清空当前邮箱
                    self.current_email = ""
                self.load_current_account()
            
            # 5. 仅使用不重建UI的排序方法
            self._sort_accounts_without_rebuild_ui()
            
            # 6. 只在有账户的情况下刷新额度数据
            if self.account_data.accounts:
                # 这里使用手动刷新模式，避免重建UI
                self.fetch_all_accounts_quota(is_manual_refresh=True, show_toast=False)
        else:
            self.show_toast("没有发现过期账户")
    
    def refresh_usage_data(self, show_toast=True):
        """刷新使用额度数据
        
        Args:
            show_toast: 是否显示Toast提示，默认显示
        """
        if not self.current_email:
            self.show_toast("未登录或登录已过期，请重新登录", error=True)
            return
            
        if show_toast:
            self.show_toast("正在获取使用额度...")
        
        # 为UI准备加载状态
        self.register_time_label.setText("注册时间: 加载中...")
        self.remaining_days_label.setSpecialText("加载中...")
        self.remaining_days_label.setColor(Theme.ACCENT)
        self.advanced_progress.setFormat("加载中...")
        self.regular_progress.setFormat("加载中...")
        
        # 使用优先加载机制获取当前账户额度数据
        QTimer.singleShot(0, lambda: self._fetch_current_account_quota())
    
    def _fetch_usage_data(self, show_toast=True):
        """在线程中获取使用额度
        
        Args:
            show_toast: 是否显示Toast提示，默认显示
        """
        try:
            # 直接从数据库中获取当前用户的token
            conn = None
            access_token = None
            
            try:
                conn = sqlite3.connect(self.auth_manager.db_path)
                cursor = conn.cursor()
                
                # 查询当前登录的accessToken
                cursor.execute("SELECT value FROM itemTable WHERE key = 'cursorAuth/accessToken'")
                result = cursor.fetchone()
                
                if result is not None:
                    access_token = result[0]
                    
            except sqlite3.Error as e:
                print(f"获取token时数据库错误: {str(e)}")
            finally:
                if conn:
                    conn.close()
            
            if not access_token:
                print(f"无法获取当前用户的accessToken")
                QTimer.singleShot(0, lambda: self._show_fetch_error())
                return
                
            # 构建账户数据以传递给AccountQuota.get_quota
            account_data = {
                "email": self.current_email,
                "auth_info": {
                    "cursorAuth/accessToken": access_token
                }
            }
            
            # 获取额度数据
            quota_data = AccountQuota.get_quota(account_data)
            
            # 仅保存数据，不立即更新UI
            if quota_data:
                # 先保存到账户额度字典中
                self.account_quotas[self.current_email] = quota_data
                
                # 延迟安全更新UI，确保UI已准备好
                QTimer.singleShot(200, lambda: self._safe_update_ui(self.current_email))
                
                # 显示成功信息（只在需要时显示）
                if show_toast:
                    QTimer.singleShot(300, lambda: self.show_toast("账户数据已更新"))
            else:
                QTimer.singleShot(0, lambda: self._show_fetch_error())
        except Exception as e:
            print(f"获取使用额度时出错: {str(e)}")
            QTimer.singleShot(0, lambda: self._show_fetch_error())
        finally:
            # 不再需要重置按钮状态
            pass
    
    def _perform_full_refresh(self, show_toast=False):
        """执行完整的数据和UI刷新
        
        Args:
            show_toast: 是否显示Toast提示，默认不显示
        """
        # 保存当前账户额度数据，避免在重新加载后丢失
        current_quota = None
        if self.current_email and self.current_email in self.account_quotas:
            current_quota = self.account_quotas[self.current_email]
        
        # 重新加载本地存储的账户数据
        self.account_data.load_accounts()
        
        # 重新加载账户到UI，但避免刷新主页
        self.load_accounts()
        
        # 更新账户数量显示
        self._update_accounts_count()
        
        # 如果有额度数据被保存，确保恢复到额度字典中
        if current_quota and self.current_email:
            self.account_quotas[self.current_email] = current_quota
        
        # 显示成功提示，但只在show_toast为True时
        if show_toast:
            self.show_toast("账户数据已更新")
    
    def _show_fetch_error(self):
        """显示获取错误信息"""
        self.advanced_progress.setFormat("获取失败")
        self.regular_progress.setFormat("获取失败")
        self.register_time_label.setText("注册时间: --")
        self.remaining_days_label.setSpecialText("获取失败")
        self.remaining_days_label.setColor(Theme.ERROR)
        self.show_toast("获取数据失败，请检查网络连接或重启应用", error=True)
    
    def _update_usage_ui(self, quota_data):
        """更新使用额度UI"""
        # 直接调用实现方法，不再使用QTimer延迟
        self._update_usage_ui_impl(quota_data)
    
    def _update_usage_ui_impl(self, quota_data):
        """在主线程中实际更新UI"""
        try:
            # 更新注册时间和剩余天数
            start_of_month = quota_data.get("startOfMonth")
            
            # 如果有真实注册时间，更新注册时间显示并重新计算剩余天数
            if start_of_month:
                try:
                    # 解析ISO 8601格式的时间字符串
                    from datetime import datetime, timedelta, timezone
                    from dateutil import parser
                    
                    # 使用dateutil解析器兼容多种时间格式
                    reg_date = parser.parse(start_of_month)
                    
                    # 获取当前时间
                    now = datetime.now(timezone.utc).replace(tzinfo=None)
                    
                    # 时区处理 - 转换为中国时间 (UTC+8)
                    china_tz = timezone(timedelta(hours=8))
                    reg_date_china = reg_date.astimezone(china_tz)
                    
                    # 格式化注册时间显示 (使用中国时间)
                    formatted_time = reg_date_china.strftime("%Y-%m-%d %H:%M:%S")
                    self.register_time_label.setText(f"注册时间: {formatted_time}")
                    
                    # 去除时区信息以便比较
                    reg_date = reg_date.replace(tzinfo=None)
                    
                    # 计算总试用期限（14天）
                    trial_period = timedelta(days=14)
                    expiry_date = reg_date + trial_period
                    
                    # 计算已经使用的时间
                    used_time = now - reg_date
                    used_days = used_time.days
                    remaining_days_float = 14 - used_days - (used_time.seconds / 86400)  # 精确到小数
                    
                    # 如果已过期或剩余不足1天
                    if now > expiry_date or remaining_days_float < 1:
                        remaining_days = "已过期"
                        self.remaining_days_label.setSpecialText(remaining_days)
                        self.remaining_days_label.setColor(Theme.ERROR)
                    else:
                        # 计算剩余天数（总试用期14天 - 已使用天数）
                        remaining_days = 14 - used_days
                        self.remaining_days_label.setValue(remaining_days, animate=True)
                        self.remaining_days_label.setColor(Theme.SUCCESS)
                    
                    # 将注册时间和剩余天数作为当前邮箱的临时信息
                    # 不再需要更新账户数据，因为我们直接从数据库获取token并实时查询
                except Exception as e:
                    print(f"计算剩余天数时出错: {str(e)}")
                    self.register_time_label.setText("--")
                    self.remaining_days_label.setSpecialText("未知")
                    self.remaining_days_label.setColor(Theme.TEXT_SECONDARY)
            else:
                # 没有真实注册时间，显示默认值
                self.register_time_label.setText("注册时间: --")
                self.remaining_days_label.setValue(0, animate=False)
                self.remaining_days_label.setColor(Theme.TEXT_SECONDARY)
            
            # 高级模型 (GPT-4)
            gpt4_data = quota_data.get("gpt-4", {})
            usage = gpt4_data.get("numRequestsTotal", 0)
            max_usage = gpt4_data.get("maxRequestUsage", 150)
            
            # 检查高级模型使用上限，当≥500时显示"尊贵的有钱人Pro用户"
            if max_usage >= 500:
                # 临时保存原始前缀并设置为空
                original_prefix = self.remaining_days_label.prefix
                self.remaining_days_label.prefix = ""
                self.remaining_days_label.setSpecialText("尊贵的有钱人Pro用户")
                self.remaining_days_label.setColor(Theme.GOLD)  # 使用金色突出显示
            
            if max_usage is not None:
                if max_usage > 0:
                    self.advanced_progress.setRange(0, max_usage)
                    self.advanced_progress.setValue(usage)  # 显示已使用量
                    self.advanced_progress.setFormat(f"{usage}/{max_usage}")
                    
                    # 根据使用情况设置不同颜色
                    usage_percent = usage / max_usage
                    if usage_percent > 0.9:
                        # 已用超过90%，显示红色警告
                        self.advanced_progress.setChunkColor(Theme.ERROR)
                    elif usage_percent > 0.7:
                        # 已用超过70%，显示黄色警告
                        self.advanced_progress.setChunkColor(Theme.WARNING)
                    else:
                        # 默认绿色
                        self.advanced_progress.setChunkColor(Theme.ACCENT)
                else:
                    self.advanced_progress.setRange(0, 100)
                    self.advanced_progress.setValue(100)
                    self.advanced_progress.setFormat("无限制")
            else:
                self.advanced_progress.setRange(0, 100)
                self.advanced_progress.setValue(0)
                self.advanced_progress.setFormat("未知")
            
            # 普通模型 (GPT-3.5)
            gpt35_data = quota_data.get("gpt-3.5-turbo", {})
            usage = gpt35_data.get("numRequestsTotal", 0)
            max_usage = gpt35_data.get("maxRequestUsage")
            
            if max_usage is not None:
                if max_usage > 0:
                    self.regular_progress.setRange(0, max_usage)
                    self.regular_progress.setValue(usage)  # 显示已使用量
                    self.regular_progress.setFormat(f"{usage}/{max_usage}")
                    
                    # 根据使用情况设置不同颜色
                    usage_percent = usage / max_usage
                    if usage_percent > 0.9:
                        # 已用超过90%，显示红色警告
                        self.regular_progress.setChunkColor(Theme.ERROR)
                    elif usage_percent > 0.7:
                        # 已用超过70%，显示黄色警告
                        self.regular_progress.setChunkColor(Theme.WARNING)
                    else:
                        # 默认绿色
                        self.regular_progress.setChunkColor(Theme.ACCENT)
                else:
                    self.regular_progress.setRange(0, 100)
                    self.regular_progress.setValue(100)
                    self.regular_progress.setFormat("无限制")
            else:
                self.regular_progress.setRange(0, 100)
                self.regular_progress.setValue(100)
                self.regular_progress.setFormat("无限制")
        except Exception as e:
            print(f"更新使用额度UI时出错: {str(e)}")
            self.advanced_progress.setFormat("获取失败")
            self.regular_progress.setFormat("获取失败")
            self.register_time_label.setText("注册时间: --")
            self.remaining_days_label.setSpecialText("未知")
            self.remaining_days_label.setColor(Theme.TEXT_SECONDARY)
    
    def refresh_accounts_data(self):
        """刷新所有账户数据"""
        # 显示Toast提示
        self.show_toast("正在获取账户数据...")
        
        # 清空账户列表
        while self.accounts_layout.count():
            item = self.accounts_layout.takeAt(0)
            if item.widget():
                item.widget().deleteLater()
        
        # 在新线程中加载数据
        thread = threading.Thread(target=self._load_accounts_data)
        thread.daemon = True
        thread.start()
    
    def _load_accounts_data(self):
        """在线程中加载账户数据"""
        try:
            # 重新加载账户数据
            self.account_data = AccountData()
            
            # 在主线程中更新UI
            QTimer.singleShot(0, self._update_accounts_ui)
        except Exception as e:
            print(f"加载账户数据时出错: {str(e)}")
            QTimer.singleShot(0, lambda: self.show_toast("加载账户数据失败", error=True))
    
    def _update_accounts_ui(self, show_toast=True):
        """在主线程中更新账户列表UI
        
        Args:
            show_toast: 是否显示Toast提示，默认显示
        """
        # 清空当前账户列表UI
        while self.accounts_layout.count():
            item = self.accounts_layout.takeAt(0)
            if item.widget():
                item.widget().deleteLater()
        
        # 重新加载当前账号信息
        self.load_current_account()
        
        # 加载账户列表
        self.load_accounts()
        
        # 更新账户计数显示
        self._update_accounts_count()
        
        # 获取所有账户额度
        if self.account_data.accounts:
            self.fetch_all_accounts_quota(show_toast=show_toast)
        else:
            # 不再添加无数据提示，只在状态栏显示
            # no_data_label = QLabel("没有找到账户数据")
            # no_data_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            # no_data_label.setStyleSheet(f"""
            #     color: {Theme.TEXT_SECONDARY};
            #     font-size: {Theme.FONT_SIZE_NORMAL};
            #     padding: 20px;
            # """)
            # self.accounts_layout.addWidget(no_data_label)
            self.show_toast("未找到账户数据，请添加账户")
            self._update_accounts_count()
    
    def load_accounts(self):
        """加载账户列表到UI"""
        # 清除之前的行
        for row in self.account_rows.values():
            row.setParent(None)
            row.deleteLater()
        self.account_rows.clear()
        
        # 定义排序键函数
        def get_register_time(account):
            # 尝试获取所有可能的注册时间字段
            api_register_time = account.get("api_register_time")
            register_time = account.get("register_time")
            
            # 优先使用API返回的注册时间，因为它是最准确的
            if api_register_time:
                try:
                    from dateutil import parser
                    dt = parser.parse(api_register_time)
                    # 确保日期时间对象没有时区信息
                    return dt.replace(tzinfo=None)
                except:
                    pass
            
            # 其次使用保存的注册时间
            if register_time:
                try:
                    from dateutil import parser
                    dt = parser.parse(register_time)
                    # 确保日期时间对象没有时区信息
                    return dt.replace(tzinfo=None)
                except:
                    pass
            
            # 如果都没有，返回一个很旧的日期
            from datetime import datetime
            return datetime(2000, 1, 1)
        
        # 加载排序后的账户数据
        sorted_accounts = sorted(
            self.account_data.accounts, 
            key=lambda acc: get_register_time(acc), 
            reverse=True
        )
        
        # 为每个账户创建一行
        for account in sorted_accounts:
            email = account.get("email", "")
            is_current = email == self.current_email
            row = AccountRowWidget(account, is_current)
            row.switch_account_signal.connect(self.switch_account)
            row.delete_account_signal.connect(self.delete_account)
            self.accounts_layout.addWidget(row)
            self.account_rows[email] = row
            
            # 初始化账户行的状态为"加载中..."
            row.set_loading_state(True)
            
        # 更新账户数量显示
        self._update_accounts_count()
    
    def fetch_all_accounts_quota(self, is_manual_refresh=False, show_toast=True):
        """获取所有账户的额度
        
        Args:
            is_manual_refresh: 是否是手动刷新（通过刷新状态按钮）
            show_toast: 是否显示Toast提示，默认显示
        """
        # 只在手动刷新（通过刷新状态按钮）或启动时显示Toast提示，且show_toast为True时
        if is_manual_refresh and show_toast:
            self.show_toast("正在获取账户额度数据...")
            
        # 当使用刷新状态按钮时，先重新从JSON文件加载账户数据
        if is_manual_refresh:
            # 重新加载账户数据
            self.account_data.load_accounts()
            
            # 更新账户行字典，确保与最新的账户数据同步
            updated_accounts = set(account.get("email", "") for account in self.account_data.accounts)
            current_accounts = set(self.account_rows.keys())
            
            # 移除不再存在的账户行
            for email in current_accounts - updated_accounts:
                if email in self.account_rows:
                    row = self.account_rows.pop(email)
                    row.setParent(None)
                    row.deleteLater()
            
            # 添加新增的账户行
            for account in self.account_data.accounts:
                email = account.get("email", "")
                if email not in self.account_rows:
                    is_current = email == self.current_email
                    row = AccountRowWidget(account, is_current)
                    row.switch_account_signal.connect(self.switch_account)
                    row.delete_account_signal.connect(self.delete_account)
                    self.accounts_layout.addWidget(row)
                    self.account_rows[email] = row
        
        # 创建额度获取器
        fetcher = QuotaFetcher(self.account_data.accounts)
        
        # 连接信号
        fetcher.account_quota_updated.connect(self.update_account_quota)
        
        # 传递是否是手动刷新的标志和是否显示Toast的标志
        if is_manual_refresh:
            # 使用Lambda函数包装参数
            fetcher.all_quotas_fetched.connect(
                lambda: self.on_all_quotas_fetched(is_manual_refresh=True, show_toast=show_toast)
            )
        else:
            # 使用Lambda函数包装参数
            fetcher.all_quotas_fetched.connect(
                lambda: self.on_all_quotas_fetched(is_manual_refresh=False, show_toast=show_toast)
            )
        
        # 开始获取
        fetcher.start_fetching()
        
        # 在账户管理页面，设置所有行为加载状态
        for row in self.account_rows.values():
            try:
                row.set_loading_state(True)
            except Exception as e:
                print(f"设置行加载状态时出错: {str(e)}")
    
    def update_account_quota(self, email, quota_data):
        """更新账户额度UI"""
        # 保存账户额度数据
        self.account_quotas[email] = quota_data
        
        # 更新账户数据中的注册时间
        start_of_month = quota_data.get("startOfMonth")
        if start_of_month:
            # 使用新方法更新账户信息，只更新临时文件中的配额数据
            self.account_data.update_account_info(
                email=email,
                register_time=start_of_month,
                quota_data=quota_data
            )
        
        # 如果是当前账户，延迟安全更新首页显示
        if email == self.current_email:
            # 使用安全更新机制更新UI
            QTimer.singleShot(100, lambda: self._safe_update_ui(email))
        else:
            # 仅更新账户行显示
            if email in self.account_rows:
                try:
                    self.account_rows[email].update_quota(quota_data)
                except Exception as e:
                    print(f"更新账户行 {email} 数据时出错: {str(e)}")
    
    def on_all_quotas_fetched(self, is_manual_refresh=False, show_toast=True):
        """所有账户额度获取完成
        
        Args:
            is_manual_refresh: 是否是手动刷新（通过刷新状态按钮）
            show_toast: 是否显示Toast提示，默认显示
        """
        try:
            # 只保存额度数据到临时文件，不修改主文件
            self.account_data.save_quotas_to_temp()
            
            # 增加延迟，确保数据保存操作完成
            QTimer.singleShot(150, lambda: self._update_after_fetch(is_manual_refresh, show_toast))
        except Exception as e:
            print(f"处理账户额度数据完成事件时出错: {str(e)}")
            # 即使出错也尝试更新UI
            QTimer.singleShot(150, lambda: self._update_after_fetch(is_manual_refresh, show_toast))
    
    def _update_after_fetch(self, is_manual_refresh=False, show_toast=True):
        """获取数据后更新UI
        
        Args:
            is_manual_refresh: 是否是手动刷新
            show_toast: 是否显示提示
        """
        try:
            # 如果是手动刷新，只更新现有组件数据，不重建UI
            if is_manual_refresh:
                # 确保使用正确、统一的排序逻辑
                self._sort_accounts_without_rebuild_ui()
                
                # 确保所有行显示正确的数据和状态
                for email, row in self.account_rows.items():
                    # 查找对应的账户数据
                    account_data = next((acc for acc in self.account_data.accounts if acc.get("email", "") == email), None)
                    if account_data:
                        # 更新行的账户数据
                        row.update_account_data(account_data)
                        # 如果有额度数据，应用额度数据
                        if email in self.account_quotas:
                            row.update_quota(self.account_quotas[email])
                
                # 重新排列行，确保顺序正确
                sorted_emails = [acc.get("email", "") for acc in self.account_data.accounts]
                for i, email in enumerate(sorted_emails):
                    if email in self.account_rows:
                        self.accounts_layout.removeWidget(self.account_rows[email])
                        self.accounts_layout.insertWidget(i, self.account_rows[email])
                
                # 显示提示，但只在show_toast为True时
                if show_toast:
                    self.show_toast("所有账户额度信息已更新")
            else:
                # 非手动刷新时，重新排序账户列表并重建UI
                self._sort_accounts_and_update_ui()
                
            # 如果已获取当前账户的额度数据，确保主页UI已更新
            if self.current_email and self.current_email in self.account_quotas:
                # 延迟更新主页UI，确保它已完全构建
                QTimer.singleShot(200, lambda: self._safe_update_ui(self.current_email))
        except Exception as e:
            print(f"更新数据后更新UI时出错: {str(e)}")
    
    def _sort_accounts_without_rebuild_ui(self):
        """只排序账户列表，但不重新创建UI组件"""
        # 如果没有账户数据，直接返回
        if not self.account_data.accounts:
            self._update_accounts_count()
            return
        
        # 定义排序函数，按注册时间从新到旧排序
        def get_register_time(account):
            try:
                # 尝试获取所有可能的注册时间字段
                register_time = (account.get("startOfMonth") or 
                               account.get("real_register_time") or 
                               account.get("api_register_time") or
                               account.get("register_time"))
                
                if not register_time:
                    # 尝试获取注册时间显示字段(可能已经格式化为本地日期格式)
                    if "注册时间" in account:
                        register_time = account["注册时间"]
                    else:
                        # 如果没有任何注册时间，将其排在最后
                        from datetime import datetime
                        return datetime(1970, 1, 1)
                
                # 使用dateutil解析日期时间并去除时区信息
                from dateutil import parser
                dt = parser.parse(register_time)
                return dt.replace(tzinfo=None)
            except Exception:
                # 解析失败时返回一个默认的旧日期
                from datetime import datetime
                return datetime(1970, 1, 1)
        
        # 对账户列表按注册时间从新到旧排序
        sorted_accounts = sorted(self.account_data.accounts, key=get_register_time, reverse=True)
        
        # 更新账户列表
        self.account_data.accounts = sorted_accounts
        
        # 保存账户数据
        self.account_data.save_accounts()
        
        # 更新账户计数
        self._update_accounts_count()
    
    def _update_accounts_count(self):
        """更新账户计数信息"""
        # 获取账户总数
        total_accounts = len(self.account_data.accounts)
        
        # 计算过期账户数
        expired_accounts = 0
        for account in self.account_data.accounts:
            remaining_days = account.get("real_remaining_days", "")
            if remaining_days == "已过期" or remaining_days == "试用Pro已过期":
                expired_accounts += 1
                
        # 计算有效账户数
        active_accounts = total_accounts - expired_accounts
        
        # 更新显示
        self.accounts_count_label.setText(f"共 {total_accounts} 个账户（{active_accounts} 个有效，{expired_accounts} 个过期）")
    
    def _copy_error_to_clipboard(self, error_info):
        """将错误信息复制到剪贴板
        
        Args:
            error_info: 错误信息字符串
        """
        try:
            # 创建剪贴板
            clipboard = QApplication.clipboard()
            # 设置文本
            clipboard.setText(error_info)
            # 显示复制成功提示
            self.toast_queue.add_message("错误信息已复制到剪贴板", duration=2000, error=False)
        except Exception as e:
            print(f"复制错误信息失败: {str(e)}")
    
    def show_toast(self, message, error=False, copy_error=False):
        """显示Toast提示
        
        Args:
            message: 消息文本
            error: 是否是错误消息，默认False
            copy_error: 是否复制错误信息到剪贴板，默认False
        """
        # 直接使用消息队列添加消息
        self.toast_queue.add_message(message, duration=2000, error=error)
        
        # 如果是错误消息且需要复制
        if error and copy_error:
            # 收集错误信息
            error_info = f"\n=== YCursor 错误信息 ===\n"
            error_info += f"错误消息: {message}\n"
            error_info += f"操作系统: {platform.system()} {platform.version()}\n"
            error_info += f"时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
            
            # 如果有原始错误信息，添加到复制内容中
            if hasattr(self, '_last_error_detail') and self._last_error_detail:
                error_info += f"\n原始错误:\n{self._last_error_detail}\n"
            
            # 复制到剪贴板
            self._copy_error_to_clipboard(error_info)
    
    def delete_expired_accounts(self):
        """删除所有过期账户"""
        # 确认是否要删除
        if not Utils.confirm_message(
            self,
            "删除过期账户",
            "确定要删除所有过期账户吗？",
        ):
            return
        
        # 执行删除，获取被删除的账户邮箱列表
        deleted_emails = self.account_data.delete_expired_accounts()
        
        if deleted_emails:
            self.show_toast(f"已删除 {len(deleted_emails)} 个过期账户")
            
            # 1. 重新加载本地存储的账户数据
            self.account_data.load_accounts()
            
            # 2. 清除已删除账户的行
            for email in deleted_emails:
                if email in self.account_rows:
                    row_widget = self.account_rows[email]
                    self.accounts_layout.removeWidget(row_widget)
                    row_widget.deleteLater()
                    del self.account_rows[email]
            
            # 3. 更新账户计数
            self._update_accounts_count()
            
            # 4. 更新当前账户UI（如果当前账户被删除）
            if self.current_email in deleted_emails:
                # 如果还有账户，切换到第一个
                if self.account_data.accounts:
                    new_current = self.account_data.accounts[0]
                    self.current_email = new_current.get("email", "")
                else:
                    # 没有账户了，清空当前邮箱
                    self.current_email = ""
                self.load_current_account()
            
            # 5. 仅使用不重建UI的排序方法
            self._sort_accounts_without_rebuild_ui()
            
            # 6. 只在有账户的情况下刷新额度数据
            if self.account_data.accounts:
                # 这里使用手动刷新模式，避免重建UI
                self.fetch_all_accounts_quota(is_manual_refresh=True, show_toast=False)
        else:
            self.show_toast("没有发现过期账户")
    
    def _sort_accounts_and_update_ui(self):
        """根据注册时间重新排序账户列表并更新UI"""
        # 如果没有账户数据，直接返回
        if not self.account_data.accounts:
            self.show_toast("未找到账户数据，请添加账户")
            self._update_accounts_count()
            return
        
        # 定义排序函数，按注册时间从新到旧排序
        def get_register_time(account):
            try:
                # 尝试获取所有可能的注册时间字段
                register_time = (account.get("startOfMonth") or 
                               account.get("real_register_time") or 
                               account.get("api_register_time") or
                               account.get("register_time"))
                
                if not register_time:
                    # 尝试获取注册时间显示字段(可能已经格式化为本地日期格式)
                    if "注册时间" in account:
                        register_time = account["注册时间"]
                    else:
                        # 如果没有任何注册时间，将其排在最后
                        from datetime import datetime
                        return datetime(1970, 1, 1)
                
                # 使用dateutil解析日期时间并去除时区信息
                from dateutil import parser
                dt = parser.parse(register_time)
                return dt.replace(tzinfo=None)
            except Exception:
                # 解析失败时返回一个默认的旧日期
                from datetime import datetime
                return datetime(1970, 1, 1)
        
        # 对账户列表按注册时间从新到旧排序
        sorted_accounts = sorted(self.account_data.accounts, key=get_register_time, reverse=True)
        
        # 更新账户列表
        self.account_data.accounts = sorted_accounts
        
        # 保存账户数据
        self.account_data.save_accounts()
        
        # 清空当前UI
        while self.accounts_layout.count():
            item = self.accounts_layout.takeAt(0)
            if item.widget():
                item.widget().deleteLater()
        
        # 重新创建账户行
        self.account_rows = {}
        for account in sorted_accounts:
            email = account.get("email", "")
            is_current = email == self.current_email
            
            # 创建行组件
            row = AccountRowWidget(account, is_current)
            
            # 如果已经有该账户的额度数据，直接更新
            if email in self.account_quotas:
                try:
                    row.update_quota(self.account_quotas[email])
                except Exception as e:
                    print(f"更新账户额度时出错: {str(e)}")
            else:
                try:
                    row.set_loading_state(True)
                except Exception as e:
                    print(f"设置行加载状态时出错: {str(e)}")
            
            # 连接信号
            row.switch_account_signal.connect(self.switch_account)
            row.delete_account_signal.connect(self.delete_account)
            
            # 添加到布局
            self.accounts_layout.addWidget(row)
            self.account_rows[email] = row
        
        # 更新账户计数
        self._update_accounts_count()

    def event(self, event):
        """处理自定义事件"""
        if event.type() == QEvent.Type.User:
            # 自定义事件用于更新UI，确保在主线程中执行
            return True
        return super().event(event)

    def _safe_update_ui(self, email):
        """安全地更新UI，确保UI已准备好
        
        Args:
            email: 要更新的账户邮箱
        """
        try:
            # 检查该邮箱是否存在额度数据
            if email not in self.account_quotas:
                print(f"错误: 邮箱 {email} 不存在额度数据")
                return
                
            # 检查是否为当前邮箱
            if email != self.current_email:
                print(f"邮箱 {email} 不是当前登录邮箱 {self.current_email}")
                return
            
            # 获取额度数据
            quota_data = self.account_quotas[email]
            
            # 首先保存数据，避免UI更新时数据冲突
            # 为主页面UI更新做准备
            QTimer.singleShot(0, lambda: self._update_usage_ui_impl(quota_data))
            
            # 刷新账户列表中的相应行（稍后执行，确保主UI先更新）
            if email in self.account_rows:
                try:
                    # 延迟更新账户行，避免与主UI更新冲突
                    QTimer.singleShot(50, lambda: self.account_rows[email].update_quota(quota_data))
                except Exception as e:
                    print(f"更新账户行数据时出错: {str(e)}")
        except Exception as e:
            print(f"安全更新UI出错: {str(e)}")
    
    def _load_current_account_quota_from_storage(self):
        """从存储的数据中加载当前账户的额度信息，而不是请求API
        
        Returns:
            bool: 加载成功返回True，否则返回False
        """
        try:
            # 获取当前邮箱
            if not self.current_email:
                print("加载存储数据失败: 当前无邮箱")
                return False
            
            # 检查是否有存储的配额数据
            if self.current_email in self.account_quotas:
                # 直接使用已存储的数据更新UI
                QTimer.singleShot(0, lambda: self._safe_update_ui(self.current_email))
                print(f"从存储加载配额成功: {self.current_email}")
                return True
            else:
                print(f"存储中没有该账户的配额数据: {self.current_email}")
                return False
        except Exception as e:
            print(f"从存储加载配额数据时发生错误: {str(e)}")
            return False
            
    def _fetch_current_account_quota(self, silent=False):
        """单独获取当前账户额度数据，优先更新首页显示
        
        该方法与_fetch_usage_data类似，但专为首页优先加载设计，
        独立于其他账户数据获取流程，确保首页尽快显示当前账户信息
        
        Args:
            silent: 是否静默获取（不主动更新UI），默认为False
        
        Returns:
            bool: 静默模式下，如果数据有变化返回True，否则返回False
        """
        try:
            # 保存旧数据用于比较（仅在静默模式下使用）
            old_quota = None
            if silent and self.current_email in self.account_quotas:
                old_quota = self.account_quotas.get(self.current_email, {}).copy()
            
            # 直接从数据库中获取当前用户的token
            conn = None
            access_token = None
            
            try:
                conn = sqlite3.connect(self.auth_manager.db_path)
                cursor = conn.cursor()
                
                # 查询当前登录的accessToken
                cursor.execute("SELECT value FROM itemTable WHERE key = 'cursorAuth/accessToken'")
                result = cursor.fetchone()
                
                if result is not None:
                    access_token = result[0]
                    
            except sqlite3.Error as e:
                print(f"{'自动刷新' if silent else '首页优先加载'}：获取token时数据库错误: {str(e)}")
                return False
            finally:
                if conn:
                    conn.close()
            
            if not access_token:
                print(f"{'自动刷新' if silent else '首页优先加载'}：无法获取当前用户的accessToken")
                return False
                
            # 构建账户数据以传递给AccountQuota.get_quota
            account_data = {
                "email": self.current_email,
                "auth_info": {
                    "cursorAuth/accessToken": access_token
                }
            }
            
            # 获取额度数据
            quota_data = AccountQuota.get_quota(account_data)
            
            # 更新并显示数据
            if quota_data:
                # 检查数据是否有变化（仅在静默模式下）
                data_changed = False
                if silent and old_quota:
                    # 比较关键字段
                    if (old_quota.get('used_tokens') != quota_data.get('used_tokens') or
                        old_quota.get('hard_limit_tokens') != quota_data.get('hard_limit_tokens') or
                        old_quota.get('startOfMonth') != quota_data.get('startOfMonth')):
                        data_changed = True
                        print(f"自动刷新: 账户 {self.current_email} 额度数据已变化")
                        
                        # 输出变化详情
                        old_used = old_quota.get('used_tokens', 0)
                        new_used = quota_data.get('used_tokens', 0)
                        if old_used != new_used:
                            print(f"  - 已用额度: {old_used} -> {new_used}")
                            
                        old_limit = old_quota.get('hard_limit_tokens', 0)
                        new_limit = quota_data.get('hard_limit_tokens', 0)
                        if old_limit != new_limit:
                            print(f"  - 总额度: {old_limit} -> {new_limit}")
                
                # 保存到账户额度字典中
                self.account_quotas[self.current_email] = quota_data
                
                # 更新账户数据中的注册时间
                start_of_month = quota_data.get("startOfMonth")
                if start_of_month:
                    # 遍历所有账户，找到对应的账户
                    for account in self.account_data.accounts:
                        if account.get("email") == self.current_email:
                            # 更新账户的注册时间
                            account["api_register_time"] = start_of_month
                            # 同时保存额度数据到账户数据中，方便从临时文件直接读取
                            account["quota_data"] = quota_data
                            # 只保存到临时文件，不修改主文件
                            self.account_data.save_quotas_to_temp()
                            break
                
                # 非静默模式或数据有变化时，更新UI
                if not silent or (silent and data_changed):
                    # 立即更新首页UI
                    QTimer.singleShot(0, lambda: self._safe_update_ui(self.current_email))
                    
                return data_changed  # 返回数据是否有变化
            
            return False
        except Exception as e:
            print(f"{'自动刷新' if silent else '首页优先加载'}：获取当前账户额度时出错: {str(e)}")
            # 出错时不显示错误提示，让后续的全局刷新再处理
            return False
    
    def _auto_refresh_current_quota(self):
        """定时自动刷新当前账户额度数据"""
        try:
            # 检查是否有当前邮箱
            if not self.current_email:
                return
                
            # 如果当前不在首页，则跳过刷新以减少不必要的资源消耗
            if self.content_stack.currentWidget() != self.home_page:
                return
                
            # 记录刷新时间，避免频繁刷新
            current_time = time.time()
            if hasattr(self, '_last_refresh_time'):
                # 如果距离上次刷新不足5秒，则跳过本次刷新
                if current_time - self._last_refresh_time < 5:
                    print(f"自动刷新: 距离上次刷新不足5秒，跳过本次刷新")
                    return
            
            # 更新最后刷新时间
            self._last_refresh_time = current_time
                
            # 保存当前额度数据用于比较
            old_quota = None
            if self.current_email in self.account_quotas:
                old_quota = self.account_quotas.get(self.current_email, {}).copy()
                
            # 先检查临时文件是否有变化
            file_changed = self._check_temp_file_changes()
            
            # 如果临时文件没有变化，再请求API获取最新数据
            if not file_changed:
                # 静默获取数据，如果数据有变化返回True
                api_data_changed = self._fetch_current_account_quota(silent=True)
                
                # 只有在数据变化时才恢复动画，并且使用延迟避免同时触发太多动画
                if api_data_changed:
                    # 延迟恢复动画以避免UI阻塞
                    QTimer.singleShot(300, lambda: self._restore_home_page_animations())
            
            # 检查刷新后的数据是否有变化（即使文件变化已经处理过，也再次确认一下）
            new_quota = self.account_quotas.get(self.current_email, {})
            if old_quota and self._compare_quota_data(old_quota, new_quota):
                print(f"检测到数据变化，更新首页UI")
                # 立即更新首页UI
                QTimer.singleShot(0, lambda: self._safe_update_ui(self.current_email))
                # 恢复动画效果
                QTimer.singleShot(300, lambda: self._restore_home_page_animations(force=True))
            
        except Exception as e:
            print(f"自动刷新当前账户额度时出错: {str(e)}")
    
    def _compare_quota_data(self, old_quota, new_quota):
        """比较两个额度数据是否有关键字段的差异
        
        Returns:
            bool: 如果有差异返回True，否则返回False
        """
        try:
            if not old_quota or not new_quota:
                return True
                
            # 比较直接字段
            if (old_quota.get('used_tokens') != new_quota.get('used_tokens') or
                old_quota.get('hard_limit_tokens') != new_quota.get('hard_limit_tokens') or
                old_quota.get('startOfMonth') != new_quota.get('startOfMonth')):
                return True
                
            # 比较GPT-4模型数据
            old_gpt4 = old_quota.get('gpt-4', {})
            new_gpt4 = new_quota.get('gpt-4', {})
            if (old_gpt4.get('numRequestsTotal') != new_gpt4.get('numRequestsTotal') or
                old_gpt4.get('maxRequestUsage') != new_gpt4.get('maxRequestUsage')):
                return True
                
            # 比较GPT-3.5模型数据
            old_gpt35 = old_quota.get('gpt-3.5-turbo', {})
            new_gpt35 = new_quota.get('gpt-3.5-turbo', {})
            if (old_gpt35.get('numRequestsTotal') != new_gpt35.get('numRequestsTotal') or
                old_gpt35.get('maxRequestUsage') != new_gpt35.get('maxRequestUsage')):
                return True
                
            return False
        except Exception:
            # 如果比较过程中出错，保守地返回True以触发更新
            return True

    def _check_temp_file_changes(self):
        """检查临时文件变化并更新UI"""
        try:
            # 重新加载临时文件中的账户数据
            temp_accounts = []
            temp_file = self.account_data.temp_file
            
            if not os.path.exists(temp_file):
                return False
                
            with open(temp_file, 'r', encoding='utf-8') as f:
                temp_accounts = json.load(f)
                
            if not temp_accounts:
                return False
                
            # 查找当前账户的数据
            updated = False
            for temp_acc in temp_accounts:
                if temp_acc.get("email") == self.current_email:
                    # 获取额度数据
                    quota_data = temp_acc.get("quota_data")
                    if not quota_data:
                        continue
                        
                    # 检查额度数据是否有变化
                    old_quota = self.account_quotas.get(self.current_email, {})
                    
                    # 比较关键字段
                    if (self._compare_quota_data(old_quota, quota_data)):
                        # 更新内存中的数据
                        self.account_quotas[self.current_email] = quota_data
                        updated = True
                        print(f"从临时文件更新了账户 {self.current_email} 的额度数据")
                    break
                    
            if updated:
                # 更新UI
                QTimer.singleShot(0, lambda: self._safe_update_ui(self.current_email))
                # 恢复动画效果
                QTimer.singleShot(100, lambda: self._restore_home_page_animations())
                
            return updated
        except Exception as e:
            print(f"检查临时文件变化时出错: {str(e)}")
            return False
            
    def _restore_home_page_animations(self, force=False):
        """在数据更新后恢复首页动画效果
        
        Args:
            force: 是否强制恢复，即使短时间内已恢复过，默认False
        """
        try:
            # 只有在当前显示的是首页时才恢复动画
            if self.content_stack.currentWidget() != self.home_page:
                return
                
            # 检查上次恢复时间，避免频繁恢复导致的卡顿
            current_time = time.time()
            if not force and hasattr(self, '_last_restore_animation_time'):
                # 如果两次恢复间隔小于1秒，则跳过
                if current_time - self._last_restore_animation_time < 1:
                    print("恢复首页动画: 距离上次恢复不足1秒，跳过本次恢复")
                    return
            
            # 更新最后恢复时间
            self._last_restore_animation_time = current_time
                
            print("恢复首页动画效果")
                
            # 查找首页中的所有进度条
            progress_bars = self.home_page.findChildren(StyledProgressBar)
            
            # 分批次恢复进度条动画，避免一次性触发太多动画
            for i, progress_bar in enumerate(progress_bars):
                try:
                    # 延迟时间随索引增加，使动画错开进行
                    delay = 100 + (i * 50)  # 每个进度条间隔50毫秒
                    QTimer.singleShot(delay, lambda bar=progress_bar: self._delayed_restore_bar(bar, force))
                except Exception as e:
                    print(f"设置进度条动画延迟恢复时出错: {str(e)}")
            
            # 查找首页中的所有数字动画标签
            animated_labels = self.home_page.findChildren(AnimatedNumberLabel)
            
            # 分批次恢复数字标签动画，且在进度条之后
            for i, label in enumerate(animated_labels):
                try:
                    # 数字标签动画在所有进度条之后开始
                    delay = 400 + (i * 50)  # 每个标签间隔50毫秒
                    QTimer.singleShot(delay, lambda lbl=label: self._delayed_restore_label(lbl, force))
                except Exception as e:
                    print(f"设置数字标签动画延迟恢复时出错: {str(e)}")
        except Exception as e:
            print(f"恢复首页动画时出错: {str(e)}")


# 应用程序主函数
def main():
    """主入口"""
    try:
        # 导入版本检查器
        from version_checker import VersionChecker
        
        # 检查是否需要隐藏控制台
        if VersionChecker.HIDE_CONSOLE:
            # Windows平台检查是否以非隐藏模式启动
            if sys.platform == "win32":
                # 判断是否从python.exe启动(有终端)
                if sys.executable.lower().endswith('python.exe') and not sys.executable.lower().endswith('pythonw.exe'):
                    import ctypes
                    kernel32 = ctypes.WinDLL('kernel32')
                    user32 = ctypes.WinDLL('user32')
                    
                    # 尝试隐藏控制台，但这可能不会完全有效
                    hwnd = kernel32.GetConsoleWindow()
                    if hwnd:
                        user32.ShowWindow(hwnd, 0)  # 0 表示 SW_HIDE
                        
                    # 最可靠的方法：使用pythonw重新启动程序
                    pythonw_path = sys.executable.replace('python.exe', 'pythonw.exe')
                    if os.path.exists(pythonw_path):
                        script_path = os.path.abspath(sys.argv[0])
                        args = [pythonw_path, script_path] + sys.argv[1:]
                        subprocess.Popen(args, creationflags=subprocess.CREATE_NO_WINDOW)
                        sys.exit(0)  # 退出当前进程
    except Exception as e:
        print(f"隐藏控制台窗口时出错: {str(e)}")
    
    app = QApplication(sys.argv)
    
    # 设置应用程序样式
    Utils.set_dark_theme(app)
    
    # 版本检查
    try:
        from version_checker import check_version
        can_continue = check_version()
        if not can_continue:
            sys.exit(0)
    except ImportError:
        # 如果没有版本检查模块，继续执行
        print("警告: 版本检查模块不存在，跳过版本检查")
    except Exception as e:
        # 捕获其他可能的错误，避免因版本检查失败而无法启动程序
        print(f"版本检查错误: {str(e)}")
    
    # 初始化主窗口
    window = CursorAccountManager()
    
    # 调整窗口大小和位置
    center_window(window)
    
    # 设置初始透明度 - 使用更高的初始透明度以减少闪烁
    window.setWindowOpacity(0.0)
    
    # 在显示窗口之前创建一个屏障，防止任何绘制操作
    window.setAttribute(Qt.WidgetAttribute.WA_DontShowOnScreen, True)
    
    # 先显示窗口但不在屏幕上显示
    window.show()
    window.hide()
    
    # 移除屏障
    window.setAttribute(Qt.WidgetAttribute.WA_DontShowOnScreen, False)
    
    # 强制完成所有挂起的布局和绘制操作
    QApplication.processEvents()
    
    # 正常显示窗口
    window.setWindowOpacity(0.01)  # 几乎不可见，但会触发初始渲染
    window.show()
    
    # 给窗口足够的时间进行初始渲染
    QApplication.processEvents()
    QThread.msleep(50)  # 短暂延迟确保完全渲染
    QApplication.processEvents()
    
    # 启动动画，加载数据的操作会在动画结束后自动执行
    window.run_entrance_animation()
    
    # 注册程序退出清理函数
    app.aboutToQuit.connect(lambda: cleanup_on_exit(window.account_data.temp_file))
    
    # 运行应用程序
    sys.exit(app.exec())

def cleanup_on_exit(temp_file_path):
    """程序退出时的清理工作"""
    try:
        # 检查并删除临时文件
        if os.path.exists(temp_file_path):
            os.remove(temp_file_path)
            print(f"程序退出：已删除临时文件 {temp_file_path}")
    except Exception as e:
        print(f"程序退出清理时出错: {str(e)}")


def center_window(window):
    """将窗口移动到屏幕中央，确保绝对居中"""
    # 获取窗口当前所在的屏幕
    window_center = window.frameGeometry().center()
    screen = QApplication.screenAt(window_center)
    if not screen:  # 如果无法确定窗口所在屏幕，使用主屏幕
        screen = QApplication.primaryScreen()
    
    screen_geometry = screen.availableGeometry()  # 使用可用区域而非整个屏幕
    
    # 更新窗口几何信息确保尺寸计算准确
    window.updateGeometry()
    window.adjustSize()
    
    # 获取准确的窗口尺寸包括边框
    window_frame = window.frameGeometry()
    
    # 计算居中位置，考虑屏幕可用区域
    center_point = screen_geometry.center()
    
    # 将窗口的中心点设置到屏幕中心点
    window_frame.moveCenter(center_point)
    dest_pos = window_frame.topLeft()
    
    # 对于无边框窗口，使用更准确的几何校正
    if window.windowFlags() & Qt.WindowType.FramelessWindowHint:
        window.setGeometry(
            dest_pos.x(),
            dest_pos.y(),
            window.width(),
            window.height()
        )
    else:
        window.move(dest_pos)


def demo_dialogs():
    """演示所有对话框样式（仅供参考）"""
    app = QApplication(sys.argv)
    Utils.set_dark_theme(app)
    
    # 创建一个主窗口
    main_window = QMainWindow()
    main_window.setWindowTitle("对话框演示")
    main_window.resize(800, 600)
    
    # 创建中心控件
    central_widget = QWidget()
    main_window.setCentralWidget(central_widget)
    
    # 创建布局
    layout = QVBoxLayout(central_widget)
    
    # 添加按钮
    info_btn = QPushButton("显示信息对话框")
    info_btn.clicked.connect(lambda: StyledDialog.showInfoDialog(main_window, "信息", "这是一个信息对话框"))
    layout.addWidget(info_btn)
    
    confirm_btn = QPushButton("显示确认对话框")
    confirm_btn.clicked.connect(lambda: StyledDialog.showConfirmDialog(main_window, "确认", "是否确认此操作？"))
    layout.addWidget(confirm_btn)
    
    warning_btn = QPushButton("显示警告对话框")
    warning_btn.clicked.connect(lambda: Utils.confirm_message(main_window, "警告", "此操作可能有风险", QMessageBox.Icon.Warning))
    layout.addWidget(warning_btn)
    
    error_btn = QPushButton("显示错误对话框")
    error_btn.clicked.connect(lambda: Utils.confirm_message(main_window, "错误", "操作失败", QMessageBox.Icon.Critical))
    layout.addWidget(error_btn)
    
    quota_btn = QPushButton("显示删除额度对话框")
    quota_btn.clicked.connect(lambda: show_demo_quota_dialog(main_window))
    layout.addWidget(quota_btn)
    
    # 显示窗口
    main_window.show()
    
    # 居中显示窗口
    center_window(main_window)
    
    sys.exit(app.exec())


def show_demo_quota_dialog(parent):
    """演示删除额度对话框"""
    dialog = StyledDialog(parent, "删除额度账户")
    
    # 添加说明文本
    description = QLabel("请选择要删除的账户条件：")
    description.setStyleSheet(f"""
        color: {Theme.TEXT_PRIMARY};
        font-size: {Theme.FONT_SIZE_NORMAL};
    """)
    dialog.addWidget(description)
    
    # 创建条件选择区域的背景框架
    conditions_frame = QFrame()
    conditions_frame.setObjectName("conditionsFrame")
    conditions_frame.setStyleSheet(f"""
        #conditionsFrame {{
            background-color: #121317;
            border-radius: {Theme.BORDER_RADIUS_SMALL};
        }}
    """)
    
    frame_layout = QVBoxLayout(conditions_frame)
    frame_layout.setContentsMargins(15, 15, 15, 15)
    
    # 创建条件选择区域
    condition_layout = QHBoxLayout()
    condition_layout.setSpacing(10)
    
    # 操作符选择
    operator_combo = QComboBox()
    operator_combo.addItems(["大于等于", "小于等于", "等于"])
    operator_combo.setStyleSheet(f"""
        QComboBox {{
            background-color: #121317;
            color: {Theme.TEXT_PRIMARY};
            border: 1px solid {Theme.BORDER};
            border-radius: {Theme.BORDER_RADIUS_SMALL};
            padding: 8px 15px 8px 15px;
            min-width: 120px;
            font-size: {Theme.FONT_SIZE_NORMAL};
        }}
        QComboBox:hover {{
            border: 1px solid {Theme.ACCENT};
            background-color: #181b21;
        }}
        QComboBox:focus {{
            border: 1px solid {Theme.ACCENT};
            background-color: #181b21;
        }}
        QComboBox::drop-down {{
            subcontrol-origin: padding;
            subcontrol-position: right center;
            width: 20px;
            border-left: none;
            padding-right: 5px;
        }}
        QComboBox::down-arrow {{
            width: 10px;
            height: 10px;
            image: none;
            border-top: 5px solid {Theme.TEXT_PRIMARY};
            border-right: 5px solid transparent;
            border-left: 5px solid transparent;
        }}
        QComboBox QAbstractItemView {{
            background-color: #121317;
            color: {Theme.TEXT_PRIMARY};
            border: 1px solid {Theme.BORDER};
            border-radius: {Theme.BORDER_RADIUS_SMALL};
            selection-background-color: {Theme.ACCENT};
            outline: none;
            padding: 5px;
        }}
        QComboBox QAbstractItemView::item {{
            padding: 8px 10px;
            min-height: 25px;
            border-radius: {Theme.BORDER_RADIUS_SMALL};
        }}
        QComboBox QAbstractItemView::item:hover {{
            background-color: #1E2128;
        }}
        QComboBox QAbstractItemView::item:selected {{
            background-color: {Theme.ACCENT};
            color: white;
        }}
    """)
    condition_layout.addWidget(operator_combo)
    
    # 额度输入
    quota_input = QLineEdit()
    quota_input.setPlaceholderText("输入额度")
    quota_input.setStyleSheet(f"""
        QLineEdit {{
            background-color: #121317;
            color: {Theme.TEXT_PRIMARY};
            border: 1px solid {Theme.BORDER};
            border-radius: {Theme.BORDER_RADIUS_SMALL};
            padding: 8px 15px;
            min-width: 120px;
            font-size: {Theme.FONT_SIZE_NORMAL};
        }}
        QLineEdit:hover {{
            border: 1px solid {Theme.ACCENT};
            background-color: #181b21;
        }}
        QLineEdit:focus {{
            border: 1px solid {Theme.ACCENT};
            background-color: #181b21;
        }}
    """)
    condition_layout.addWidget(quota_input)
    
    # 添加条件布局到框架
    frame_layout.addLayout(condition_layout)
    
    # 添加框架到对话框
    dialog.addWidget(conditions_frame)
    
    # 添加确认和取消按钮
    confirm_btn = dialog.addButtons("确认", "取消")
    confirm_btn.clicked.connect(dialog.accept)
    
    dialog.exec()


# 添加一个自定义的无下划线菜单按钮类
class NoUnderlineMenuButton(QPushButton):
    """没有下划线的菜单按钮"""
    
    def __init__(self, text, parent=None):
        super().__init__(text, parent)
        self.setStyleSheet("""
            QPushButton {
                padding: 12px 20px;
                text-align: left;
                font-size: 16px;
                border: none;
                border-bottom: none;
                background-color: transparent;
                color: #9CA2AE;
                font-weight: normal;
                border-radius: 10px;
                text-decoration: none;
            }
            QPushButton:hover {
                background-color: #1A1D23;
                color: #FFFFFF;
            }
            QPushButton:checked {
                background-color: #2B9D7C;
                color: #FFFFFF;
                font-weight: bold;
                border-bottom: none;
            }
        """)
        
    def paintEvent(self, event):
        # 自定义绘制，确保不会出现下划线
        super().paintEvent(event)
        
    def focusInEvent(self, event):
        # 阻止显示焦点框
        super().focusInEvent(event)
        self.clearFocus()


# 增加支持数字动画的进度条
class AnimatedProgressBar(StyledProgressBar):
    """支持额度数字平滑递增动画的进度条"""
    
    def __init__(self, parent=None, text_duration=2000, value_duration=1500):
        """
        初始化带数字动画的进度条
        
        Args:
            parent: 父窗口部件
            text_duration: 文本动画持续时间(毫秒)
            value_duration: 值动画持续时间(毫秒)
        """
        super().__init__(parent)
        
        # 额外的动画变量
        self._from_number = 0
        self._to_number = 0
        self._current_number = 0
        self._denominator = 150  # 默认分母
        self._text_animation = QPropertyAnimation(self, b"numberValue")
        self._text_animation.setEasingCurve(QEasingCurve.Type.OutQuad)
        self._text_animation.setDuration(text_duration)
        self._text_animation.valueChanged.connect(self._updateAnimatedText)
        self._text_animation.finished.connect(self._onTextAnimationFinished)
    
    def reset_animation(self):
        """重置并重新启动数字动画，与进度条填充动画同步"""
        try:
            # 停止任何正在运行的动画
            if self._text_animation.state() == QAbstractAnimation.State.Running:
                self._text_animation.stop()
                
            # 获取数据来源的优先级：
            # 1. 进度条动画的目标值（如果动画正在运行）
            # 2. 进度条当前值
            # 3. 持久值存储
            # 4. 格式字符串中提取的值
            
            # 初始化目标值
            target_value = 0
            
            # 优先级1：使用进度条动画的目标值
            if hasattr(self, '_animation') and self._animation.state() == QAbstractAnimation.State.Running:
                target_value = self._animation.endValue()
            # 优先级2：使用进度条的当前值
            elif self.value() > 0:
                target_value = self.value()
            # 优先级3：使用持久值存储
            elif hasattr(self, '_persistent_value') and self._persistent_value > 0:
                target_value = self._persistent_value
            # 优先级4：尝试从格式中提取
            else:
                try:
                    import re
                    format_str = self.format()
                    match = re.match(r"^(\d+)/(\d+)$", format_str)
                    if match:
                        target_value = int(match.group(1))
                except:
                    pass
            
            # 只有在有目标值时才重设动画
            if target_value > 0:
                # 重置当前值为0，与进度条填充动画一致
                self._current_number = 0
                self._to_number = float(target_value)
                
                # 配置动画从0到目标值，与进度条填充动画一致
                self._text_animation.setStartValue(0.0)
                self._text_animation.setEndValue(self._to_number)
                
                # 同步动画持续时间与进度条
                if hasattr(self, '_animation') and self._animation.state() == QAbstractAnimation.State.Running:
                    # 使用与进度条相同的动画持续时间
                    self._text_animation.setDuration(self._animation.duration())
                    # 使用与进度条相同的缓动曲线
                    self._text_animation.setEasingCurve(self._animation.easingCurve())
                    # 获取当前进度条动画的已播放时间，并从相同时间点开始数字动画
                    elapsed = self._animation.currentTime()
                    self._text_animation.setCurrentTime(elapsed)
                else:
                    # 使用与进度条相同的动画持续时间
                    self._text_animation.setDuration(self._animation.duration())
                    # 使用与进度条相同的缓动曲线
                    self._text_animation.setEasingCurve(self._animation.easingCurve())
                    
                # 更新文本以显示初始值
                self._updateAnimatedText()
                
                # 启动动画
                self._text_animation.start()
        except Exception as e:
            print(f"重置进度条数字动画出错: {str(e)}")
            # 出错时尝试直接设置最终值
            try:
                if self.value() > 0:
                    self._current_number = float(self.value())
                    self._updateAnimatedText()
            except:
                pass
    
    def getNumberValue(self):
        """获取动画数字值"""
        return self._current_number
    
    def setNumberValue(self, value):
        """设置动画数字值"""
        self._current_number = value
        self._updateAnimatedText()
    
    # 定义动画属性
    numberValue = pyqtProperty(float, getNumberValue, setNumberValue)
    
    def _updateAnimatedText(self):
        """更新动画数字格式"""
        # 检查是否为无限制模式
        if self._current_format == "无限制":
            # 无需更改格式，保持显示"无限制"
            return
            
        # 对于数字/最大值格式，更新为动画数字
        num = round(self._current_number)
        # 设置格式字符串（如"9/150"）但不触发父类的setFormat
        self._current_format = f"{num}/{self._denominator}"
        self.update()  # 强制重绘
    
    def _onTextAnimationFinished(self):
        """文本动画完成后的回调"""
        # 确保最终显示准确的数值
        self._current_number = self._to_number
        self._updateAnimatedText()
    
    def setValue(self, value):
        """重写setValue方法，为条形图添加动画，同时同步更新数字动画"""
        try:
            # 获取当前值和最大值
            max_value = self.maximum()
            
            # 检查当前格式，判断是否为无限制模式
            if self.format() == "无限制" or max_value == 100 and value == 100:
                # 对于无限制情况，调用父类的setValue方法设置进度条的值动画
                super().setValue(value)
                return
                
            # 如果最大值有效，自动更新格式文本，触发数字动画
            if max_value > 0:
                # 设置格式为"新值/最大值"，这会触发数字动画
                self.setFormat(f"{value}/{max_value}")
                
            # 调用父类的setValue方法设置进度条的值动画
            super().setValue(value)
        except Exception as e:
            print(f"设置进度条值和数字动画出错: {str(e)}")
            # 出错时简单调用父类方法
            super().setValue(value)
            
    def restore_original_value(self):
        """恢复原始值并同步启动数字动画"""
        try:
            # 重置数字动画当前值为0
            self._current_number = 0
            # 更新界面显示
            self._updateAnimatedText()
            
            # 调用父类的restore_original_value方法触发进度条填充动画
            # 这会优先使用_persistent_value，其次是_original_value，最后尝试从格式提取
            super().restore_original_value()
            
            # 确保数字动画也正确启动 - 使用延迟确保进度条动画已正确设置
            QTimer.singleShot(10, self.reset_animation)
        except Exception as e:
            print(f"恢复动画进度条原始值时出错: {str(e)}")
            try:
                # 出错时尝试使用父类方法
                super().restore_original_value()
            except:
                pass
    
    def setFormat(self, format_str):
        """重写setFormat方法，检测是否为"数字/最大值"格式并添加动画"""
        # 立即存储格式用于绘制
        self._current_format = format_str
        
        # 检查是否是"数字/最大值"格式（如"9/150"）
        import re
        match = re.match(r"^(\d+)/(\d+)$", format_str)
        
        if match:
            try:
                # 提取分子和分母
                numerator = int(match.group(1))
                self._denominator = int(match.group(2))
                
                # 当页面切换后，进度条尚未完全初始化，或进度条动画尚未启动
                # 直接将当前值设为0，确保数字动画能启动
                if self._animation.state() != QAbstractAnimation.State.Running:
                    self._current_number = 0
                
                # 停止任何运行中的动画
                if self._text_animation.state() == QAbstractAnimation.State.Running:
                    self._text_animation.stop()
                
                # 设置动画目标值
                self._to_number = float(numerator)
                
                # 获取进度条动画状态
                progress_animation_running = self._animation.state() == QAbstractAnimation.State.Running
                
                # 如果进度条正在运行动画，或者页面刚刚切换
                if progress_animation_running:
                    # 配置并启动动画，从0开始
                    self._text_animation.setStartValue(0.0)
                    self._text_animation.setEndValue(self._to_number)
                    self._text_animation.setDuration(self._animation.duration())
                    self._text_animation.setEasingCurve(self._animation.easingCurve())
                    self._text_animation.start()
                else:
                    # 如果进度条没有动画，直接设置值
                    self._current_number = self._to_number
                    self._updateAnimatedText()
            except Exception as e:
                print(f"解析进度条格式出错: {str(e)}")
                # 出错时使用默认方法
                super().setFormat(format_str)
        else:
            # 对于其他格式，直接使用父类的方法
            super().setFormat(format_str)
    
    def paintEvent(self, event):
        """自定义绘制事件，使用动画的文本值"""
        super().paintEvent(event)


if __name__ == "__main__":
    # 直接调用main()函数，它会处理隐藏终端窗口和程序初始化
    main()

# 打包说明:
# 使用PyInstaller打包为无终端窗口的程序:
# Windows: pyinstaller --noconsole --onefile YCursor.py
# macOS: pyinstaller --windowed --onefile YCursor.py 
# Linux: pyinstaller --noconsole --onefile YCursor.py 