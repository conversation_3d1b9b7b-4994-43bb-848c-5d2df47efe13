#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json
from urllib.parse import urlparse, parse_qs

# ================================
# 配置区域 - 在这里修改你的_session
# ================================
SESSION_TOKEN = "**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************%3D%3D.0Xf9OFGtLPOEE1XHOwacy%2BOg38Piz6VfRATn63XgnlU"

def get_subscription_info():
    """获取subscription信息并提取portal token"""
    
    url = "https://app.augmentcode.com/api/subscription"
    
    headers = {
        'accept': '*/*',
        'accept-language': 'zh-CN,zh;q=0.9',
        'priority': 'u=1, i',
        'referer': 'https://app.augmentcode.com/account/subscription',
        'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'same-origin',
        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
    }
    
    cookies = {
        '_session': SESSION_TOKEN,
        'ajs_user_id': '5d9f627a-90f9-42bf-8b9c-ab67ec616bc5',
        'ajs_anonymous_id': '5a962ac3-9fdf-41d8-846f-db313f781e03'
    }
    
    try:
        print("正在请求subscription信息...")
        response = requests.get(url, headers=headers, cookies=cookies)
        
        if response.status_code == 200:
            data = response.json()
            print("✅ 请求成功!")
            
            # 打印完整的响应数据（可选）
            print("\n📋 完整响应数据:")
            print(json.dumps(data, indent=2, ensure_ascii=False))
            
            # 提取portalUrl中的token
            portal_url = data.get('portalUrl', '')
            if portal_url:
                print(f"\n🔗 Portal URL: {portal_url}")
                
                # 解析URL获取token参数
                parsed_url = urlparse(portal_url)
                query_params = parse_qs(parsed_url.query)
                token = query_params.get('token', [None])[0]
                
                if token:
                    print(f"\n🎯 提取的Token:")
                    print(f"Token: {token}")
                    return token
                else:
                    print("❌ 未找到token参数")
                    return None
            else:
                print("❌ 响应中未找到portalUrl")
                return None
                
        else:
            print(f"❌ 请求失败，状态码: {response.status_code}")
            print(f"响应内容: {response.text}")
            return None
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求出现异常: {e}")
        return None
    except json.JSONDecodeError as e:
        print(f"❌ JSON解析失败: {e}")
        return None
    except Exception as e:
        print(f"❌ 未知错误: {e}")
        return None

def main():
    print("=== Augment Code Subscription Token 提取工具 ===\n")
    
    if not SESSION_TOKEN:
        print("❌ 请在脚本顶部配置SESSION_TOKEN")
        return
    
    token = get_subscription_info()
    
    if token:
        print(f"\n🎉 成功提取Token: {token}")
    else:
        print("\n💔 Token提取失败")

if __name__ == "__main__":
    main() 