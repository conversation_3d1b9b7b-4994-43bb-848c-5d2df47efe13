#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json
from datetime import datetime, timedelta

# ================================
# 配置区域 - 在这里配置你的token
# ================================
# 从 subscription_fetcher.py 获取的 portal token
PORTAL_TOKEN = "ImZEY0Zpc1BacFhLVEw5amMi.CWwZHSGS04lFSHT8TU_FdJT9x28"

class OrbAccountInfo:
    def __init__(self, token):
        self.token = token
        self.base_url = "https://portal.withorb.com/api/v1"
        self.headers = {
            'accept': 'application/json, text/plain, */*',
            'accept-language': 'zh-CN,zh;q=0.9',
            'priority': 'u=1, i',
            'referer': f'https://portal.withorb.com/view?token={token}',
            'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-origin',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
        }
        
        # 存储账号信息
        self.customer_id = None
        self.pricing_unit_id = None
        self.email = None
        self.plan_name = None
        self.status = None
        self.end_date = None
        self.credits_balance = None
        self.total_credits = None
    
    def convert_to_beijing_time(self, utc_time_str):
        """将UTC时间转换为北京时间"""
        try:
            if not utc_time_str or utc_time_str == 'N/A':
                return 'N/A'
            
            # 解析ISO格式的UTC时间
            # 处理带有+00:00或Z结尾的格式
            if utc_time_str.endswith('Z'):
                utc_time_str = utc_time_str[:-1] + '+00:00'
            
            # 解析时间字符串
            if '+' in utc_time_str:
                dt_str, tz_str = utc_time_str.rsplit('+', 1)
                utc_dt = datetime.fromisoformat(dt_str)
            else:
                utc_dt = datetime.fromisoformat(utc_time_str.replace('Z', ''))
            
            # 转换为北京时间（UTC+8）
            beijing_dt = utc_dt + timedelta(hours=8)
            
            # 格式化为中文易读格式
            return beijing_dt.strftime('%Y年%m月%d日 %H:%M:%S (北京时间)')
            
        except Exception as e:
            print(f"⚠️ 时间转换失败: {e}")
            return utc_time_str  # 如果转换失败，返回原始时间
    
    def get_subscriptions(self):
        """获取订阅信息 - 提取邮箱、计划、状态、到期时间"""
        url = f"{self.base_url}/subscriptions_from_link"
        params = {
            'token': self.token
        }
        
        try:
            print("📋 正在获取订阅信息...")
            response = requests.get(url, headers=self.headers, params=params)
            
            if response.status_code == 200:
                data = response.json()
                if data.get('data') and len(data['data']) > 0:
                    subscription = data['data'][0]
                    
                    # 提取客户信息
                    customer = subscription.get('customer', {})
                    self.customer_id = customer.get('id')
                    self.email = customer.get('name')  # name字段就是邮箱
                    
                    # 提取订阅信息
                    self.subscription_id = subscription.get('id')  # 保存subscription_id
                    self.plan_name = subscription.get('name', 'N/A')
                    self.status = subscription.get('status', 'N/A')

                    # 获取并转换到期时间
                    end_date_utc = subscription.get('end_date', 'N/A')
                    self.end_date = self.convert_to_beijing_time(end_date_utc)

                    # 提取pricing_unit_id用于获取余额，同时保存price_id
                    price_intervals = subscription.get('price_intervals', [])
                    for interval in price_intervals:
                        allocation = interval.get('allocation')
                        if allocation and allocation.get('pricing_unit'):
                            self.pricing_unit_id = allocation['pricing_unit']['id']
                            break

                    # 查找User Message相关的price_id (用于usage API)
                    for interval in price_intervals:
                        price = interval.get('price', {}).get('price', {})
                        if price.get('name') == 'User Message':
                            self.price_id = interval.get('price', {}).get('id')
                            break
                    
                    print(f"✅ 邮箱: {self.email}")
                    print(f"✅ 计划: {self.plan_name}, 状态: {self.status}")
                    return True
                else:
                    print("❌ 订阅数据为空")
                    return False
            else:
                print(f"❌ 获取订阅信息失败，状态码: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ 获取订阅信息时出现异常: {e}")
            return False
    
    def get_ledger_summary(self):
        """获取账户余额信息"""
        if not self.customer_id or not self.pricing_unit_id:
            print("⚠️ 无法获取余额信息（缺少必要参数）")
            return False

        url = f"{self.base_url}/customers/{self.customer_id}/ledger_summary"
        params = {
            'pricing_unit_id': self.pricing_unit_id,
            'token': self.token
        }

        try:
            print("💰 正在获取余额信息...")
            response = requests.get(url, headers=self.headers, params=params)

            if response.status_code == 200:
                data = response.json()

                # 提取剩余额度
                credits_balance_str = data.get('credits_balance', '0')
                self.credits_balance = float(credits_balance_str)
                print(f"🔍 调试: credits_balance原始值='{credits_balance_str}', 转换后={self.credits_balance}")

                # 🔧 修复：计算总额度需要从subscriptions API获取所有allocations
                # 这里先临时计算，稍后会从subscriptions API获取准确数据
                credit_blocks = data.get('credit_blocks', [])
                self.ledger_total_credits = 0

                if credit_blocks:
                    for i, block in enumerate(credit_blocks):
                        max_balance = block.get('maximum_initial_balance')
                        current_balance = block.get('balance')
                        is_active = block.get('is_active', False)
                        allocation_id = block.get('allocation_id')

                        print(f"🔍 Credit Block {i+1}: max={max_balance}, balance={current_balance}, active={is_active}, allocation_id={allocation_id}")

                        # 只计算有实际余额且不是allocation产生的blocks
                        # allocation_id为None表示这是基础额度，不是allocation产生的
                        if (max_balance is not None and
                            current_balance is not None and
                            is_active and
                            allocation_id is None):
                            try:
                                self.ledger_total_credits += float(max_balance)
                                print(f"🔍 添加到基础额度: {max_balance} (非allocation)")
                            except (ValueError, TypeError):
                                continue

                print(f"🔍 调试: Ledger总额度={self.ledger_total_credits}, 剩余额度={self.credits_balance}")

                # � 修复：获取真实的总额度和使用量
                self.total_credits = self.get_real_total_credits()
                self.used_credits = self.total_credits - self.credits_balance

                print(f"🔍 调试: 真实总额度={self.total_credits}, 真实使用量={self.used_credits}")
                print(f"✅ 余额获取成功")
                return True
            else:
                print(f"❌ 获取余额失败，状态码: {response.status_code}")
                print(f"响应内容: {response.text}")
                return False

        except Exception as e:
            print(f"❌ 获取余额时出现异常: {e}")
            return False



    def get_real_total_credits(self):
        """获取真实总额度 - 基于当前有效期内的所有credit blocks"""
        try:
            from datetime import datetime, timezone
            current_time = datetime.now(timezone.utc)

            # 重新获取credit_blocks数据以确保准确性
            url = f"https://portal.withorb.com/api/v1/customers/{self.customer_id}/ledger_summary"
            params = {
                'pricing_unit_id': self.pricing_unit_id,
                'token': self.token
            }

            response = requests.get(url, headers=self.headers, params=params)
            if response.status_code == 200:
                data = response.json()
                credit_blocks = data.get('credit_blocks', [])

                total_credits = 0
                print("🔍 分析当前有效的credit blocks:")

                for i, block in enumerate(credit_blocks):
                    max_balance = block.get('maximum_initial_balance')
                    effective_date = block.get('effective_date')
                    expiry_date = block.get('expiry_date')
                    is_active = block.get('is_active', False)
                    allocation_id = block.get('allocation_id')

                    print(f"🔍 Block {i+1}: max={max_balance}, effective={effective_date}, expiry={expiry_date}, active={is_active}")

                    # 检查是否应该计入总额度
                    if max_balance is not None and is_active:
                        should_include = False

                        try:
                            # 解析日期
                            effective_dt = datetime.fromisoformat(effective_date.replace('Z', '+00:00'))
                            expiry_dt = datetime.fromisoformat(expiry_date.replace('Z', '+00:00'))

                            print(f"🔍 时间比较: effective={effective_dt}, current={current_time}, expiry={expiry_dt}")

                            # 只计算当前有效期内的credit blocks
                            if effective_dt <= current_time <= expiry_dt:
                                should_include = True
                                print(f"🔍 ✅ Block {i+1} 当前有效，添加到总额度: {max_balance}")
                            else:
                                print(f"🔍 ❌ Block {i+1} 不在当前有效期内，不计入")

                        except Exception as e:
                            print(f"🔍 ⚠️ Block {i+1} 日期解析失败: {e}")
                            # 如果日期解析失败，保守处理，认为有效
                            should_include = True
                            print(f"🔍 ⚠️ Block {i+1} 日期解析失败，保守处理为有效: {max_balance}")

                        if should_include:
                            try:
                                total_credits += float(max_balance)
                            except (ValueError, TypeError):
                                continue

                print(f"🔍 当前有效总额度: {total_credits}")
                return total_credits

            else:
                print(f"⚠️ 获取ledger失败: {response.status_code}")
                return 0

        except Exception as e:
            print(f"⚠️ 获取总额度时出现异常: {e}")
            return 0
    
    def display_summary(self):
        """显示简化的账号信息"""
        print("\n" + "="*50)
        print("📊 账号信息")
        print("="*50)
        
        # 判断有效性
        validity = "✅ 有效" if self.status == "active" else "❌ 无效"
        
        # 🔧 修复：使用真实使用量
        usage_display = "N/A"
        if hasattr(self, 'used_credits') and self.total_credits is not None:
            used_credits = self.used_credits
            if self.total_credits > 0:
                usage_percent = (used_credits / self.total_credits) * 100
                usage_display = f"{used_credits:.0f}/{self.total_credits:.0f} ({usage_percent:.1f}%)"
            else:
                usage_display = f"{used_credits:.0f}/{self.total_credits:.0f} (0%)"
        
        print(f"📧 邮箱: {self.email or 'N/A'}")
        print(f"📋 计划: {self.plan_name or 'N/A'}")
        print(f"🔄 有效性: {validity}")
        print(f"💰 使用量: {usage_display}")
        print(f"📅 到期时间: {self.end_date or 'N/A'}")
    
    def fetch_account_info(self):
        """获取账号信息"""
        print("🚀 开始获取账号信息...")

        # 获取订阅信息（包含邮箱、计划、状态、到期时间）
        if not self.get_subscriptions():
            print("💔 获取订阅信息失败，无法继续")
            return False

        # 获取真实总额度（基于当前有效的credit blocks）
        if self.pricing_unit_id:
            self.total_credits = self.get_real_total_credits()

            # 获取当前剩余额度
            url = f"https://portal.withorb.com/api/v1/customers/{self.customer_id}/ledger_summary"
            params = {
                'pricing_unit_id': self.pricing_unit_id,
                'token': self.token
            }

            response = requests.get(url, headers=self.headers, params=params)
            if response.status_code == 200:
                data = response.json()
                self.credits_balance = float(data.get('credits_balance', 0))

                # 计算已使用额度
                self.used_credits = self.total_credits - self.credits_balance

                print(f"🔍 最终计算结果:")
                print(f"🔍 总额度: {self.total_credits}")
                print(f"🔍 剩余额度: {self.credits_balance}")
                print(f"🔍 已使用: {self.used_credits}")
                print(f"🔍 使用率: {(self.used_credits / self.total_credits * 100) if self.total_credits > 0 else 0:.1f}%")
            else:
                print(f"⚠️ 获取剩余额度失败: {response.status_code}")

        # 显示简化摘要
        self.display_summary()

        return True

def main():
    print("=== Augment Code账号信息获取工具 ===\n")
    
    if not PORTAL_TOKEN:
        print("❌ 请在脚本顶部配置PORTAL_TOKEN")
        print("提示：使用 subscription_fetcher.py 获取token")
        return
    
    # 创建账号信息获取器
    account = OrbAccountInfo(PORTAL_TOKEN)
    
    # 获取账号信息
    success = account.fetch_account_info()
    
    if success:
        print(f"\n🎉 账号信息获取完成!")
    else:
        print(f"\n💔 账号信息获取失败")

if __name__ == "__main__":
    main() 