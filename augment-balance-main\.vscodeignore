# VS Code files
.vscode/**
.vscode-test/**

# Source files (only keep bundled JS)
src/**
out/**
**/*.ts
**/*.map

# Configuration files
.gitignore
.yarnrc
vsc-extension-quickstart.md
**/tsconfig.json
**/.eslintrc.json
.eslintrc.*

# Test files
.nyc_output
coverage
**/test/**
**/*.test.*
**/*.spec.*

# Documentation and development files
**/.DS_Store
**/*.md
!README.md
INSTALLATION.md

# Package files
**/*.vsix
**/*.tgz
**/package-lock.json
**/yarn.lock

# Node modules - completely exclude since we use webpack bundling
node_modules/**
